"""
智能并发控制器
提供动态速率限制、资源管理和负载均衡功能
"""

import asyncio
import logging
import time
from collections import defaultdict, deque
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class RateLimitConfig:
    """速率限制配置"""
    requests_per_second: float = 1.0
    burst_size: int = 5
    backoff_factor: float = 1.5
    max_backoff: float = 60.0
    recovery_factor: float = 0.9

class AdaptiveRateLimiter:
    """自适应速率限制器"""
    
    def __init__(self, provider: str, config: RateLimitConfig):
        self.provider = provider
        self.config = config
        self._lock = asyncio.Lock()
        
        # 令牌桶算法参数
        self._tokens = config.burst_size
        self._last_update = time.time()
        
        # 自适应参数
        self._current_rate = config.requests_per_second
        self._consecutive_errors = 0
        self._last_success_time = time.time()
        
        # 统计信息
        self._request_count = 0
        self._error_count = 0
        self._total_wait_time = 0.0

    async def acquire(self) -> float:
        """获取请求许可，返回等待时间"""
        async with self._lock:
            now = time.time()
            
            # 更新令牌桶
            time_passed = now - self._last_update
            self._tokens = min(
                self.config.burst_size,
                self._tokens + time_passed * self._current_rate
            )
            self._last_update = now
            
            if self._tokens >= 1.0:
                # 有可用令牌
                self._tokens -= 1.0
                self._request_count += 1
                return 0.0
            else:
                # 需要等待
                wait_time = (1.0 - self._tokens) / self._current_rate
                self._total_wait_time += wait_time
                
                logger.debug(f"{self.provider}: 速率限制等待 {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
                
                self._tokens = 0.0
                self._request_count += 1
                return wait_time

    def report_success(self):
        """报告请求成功"""
        self._consecutive_errors = 0
        self._last_success_time = time.time()
        
        # 逐渐恢复速率
        if self._current_rate < self.config.requests_per_second:
            self._current_rate = min(
                self.config.requests_per_second,
                self._current_rate * (1 + self.config.recovery_factor * 0.1)
            )

    def report_error(self, is_rate_limit_error: bool = False):
        """报告请求错误"""
        self._error_count += 1
        
        if is_rate_limit_error:
            self._consecutive_errors += 1
            
            # 降低请求速率
            backoff_multiplier = self.config.backoff_factor ** self._consecutive_errors
            new_rate = self.config.requests_per_second / backoff_multiplier
            
            # 限制最大退避
            min_rate = 1.0 / self.config.max_backoff
            self._current_rate = max(min_rate, new_rate)
            
            logger.warning(
                f"{self.provider}: 遇到速率限制，降低请求速率至 {self._current_rate:.3f} req/s"
            )

    def get_stats(self) -> Dict[str, float]:
        """获取统计信息"""
        return {
            'current_rate': self._current_rate,
            'request_count': self._request_count,
            'error_count': self._error_count,
            'error_rate': self._error_count / max(1, self._request_count),
            'avg_wait_time': self._total_wait_time / max(1, self._request_count),
            'consecutive_errors': self._consecutive_errors
        }

class ConcurrencyController:
    """并发控制器"""
    
    def __init__(self):
        self._rate_limiters: Dict[str, AdaptiveRateLimiter] = {}
        self._semaphores: Dict[str, asyncio.Semaphore] = {}
        self._active_requests: Dict[str, int] = defaultdict(int)
        
        # 全局并发限制
        self._global_semaphore = asyncio.Semaphore(10)  # 全局最大并发数
        
        # 性能监控
        self._request_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # 默认配置
        self._default_configs = {
            'bilibili': RateLimitConfig(requests_per_second=2.0, burst_size=3),
            'tencent': RateLimitConfig(requests_per_second=3.0, burst_size=5),
            'iqiyi': RateLimitConfig(requests_per_second=2.5, burst_size=4),
            'youku': RateLimitConfig(requests_per_second=2.0, burst_size=3),
            'gamer': RateLimitConfig(requests_per_second=1.5, burst_size=2),
            'default': RateLimitConfig(requests_per_second=1.0, burst_size=2)
        }

    def get_rate_limiter(self, provider: str) -> AdaptiveRateLimiter:
        """获取或创建速率限制器"""
        if provider not in self._rate_limiters:
            config = self._default_configs.get(provider, self._default_configs['default'])
            self._rate_limiters[provider] = AdaptiveRateLimiter(provider, config)
        return self._rate_limiters[provider]

    def get_semaphore(self, provider: str, max_concurrent: int = 3) -> asyncio.Semaphore:
        """获取或创建信号量"""
        if provider not in self._semaphores:
            self._semaphores[provider] = asyncio.Semaphore(max_concurrent)
        return self._semaphores[provider]

    async def execute_with_control(
        self, 
        provider: str, 
        coro_func, 
        *args, 
        max_concurrent: int = 3,
        **kwargs
    ):
        """在并发控制下执行协程"""
        rate_limiter = self.get_rate_limiter(provider)
        semaphore = self.get_semaphore(provider, max_concurrent)
        
        # 全局并发控制
        async with self._global_semaphore:
            # 提供商级别并发控制
            async with semaphore:
                # 速率限制
                wait_time = await rate_limiter.acquire()
                
                # 记录活跃请求
                self._active_requests[provider] += 1
                start_time = time.time()
                
                try:
                    result = await coro_func(*args, **kwargs)
                    
                    # 记录成功
                    rate_limiter.report_success()
                    execution_time = time.time() - start_time
                    self._request_times[provider].append(execution_time)
                    
                    return result
                    
                except Exception as e:
                    # 判断是否为速率限制错误
                    is_rate_limit = self._is_rate_limit_error(e)
                    rate_limiter.report_error(is_rate_limit)
                    
                    if is_rate_limit:
                        logger.warning(f"{provider}: 遇到速率限制错误: {e}")
                    
                    raise
                    
                finally:
                    self._active_requests[provider] -= 1

    def _is_rate_limit_error(self, error: Exception) -> bool:
        """判断是否为速率限制错误"""
        error_str = str(error).lower()
        rate_limit_indicators = [
            'rate limit', 'too many requests', '429', 'quota exceeded',
            'api limit', 'throttle', 'slow down', '503'
        ]
        return any(indicator in error_str for indicator in rate_limit_indicators)

    def get_provider_stats(self, provider: str) -> Dict[str, any]:
        """获取提供商统计信息"""
        rate_limiter = self._rate_limiters.get(provider)
        request_times = self._request_times.get(provider, deque())
        
        stats = {
            'provider': provider,
            'active_requests': self._active_requests[provider],
            'avg_response_time': 0.0,
            'recent_requests': len(request_times)
        }
        
        if rate_limiter:
            stats.update(rate_limiter.get_stats())
        
        if request_times:
            stats['avg_response_time'] = sum(request_times) / len(request_times)
        
        return stats

    def get_global_stats(self) -> Dict[str, any]:
        """获取全局统计信息"""
        total_active = sum(self._active_requests.values())
        all_providers = set(self._rate_limiters.keys()) | set(self._active_requests.keys())
        
        provider_stats = [
            self.get_provider_stats(provider) 
            for provider in all_providers
        ]
        
        return {
            'total_active_requests': total_active,
            'global_semaphore_available': self._global_semaphore._value,
            'providers': provider_stats,
            'timestamp': datetime.now().isoformat()
        }

    async def adjust_limits_dynamically(self):
        """动态调整限制参数"""
        for provider, rate_limiter in self._rate_limiters.items():
            stats = rate_limiter.get_stats()
            
            # 如果错误率过高，进一步降低速率
            if stats['error_rate'] > 0.1:  # 10%错误率
                current_rate = rate_limiter._current_rate
                rate_limiter._current_rate = max(
                    0.1,  # 最低速率
                    current_rate * 0.8
                )
                logger.info(f"{provider}: 错误率过高，调整速率至 {rate_limiter._current_rate:.3f}")
            
            # 如果响应时间过长，降低并发
            request_times = self._request_times.get(provider, deque())
            if request_times and len(request_times) > 10:
                avg_time = sum(request_times) / len(request_times)
                if avg_time > 10.0:  # 平均响应时间超过10秒
                    semaphore = self._semaphores.get(provider)
                    if semaphore and semaphore._initial_value > 1:
                        # 减少并发数（这需要重新创建信号量）
                        new_concurrent = max(1, semaphore._initial_value - 1)
                        self._semaphores[provider] = asyncio.Semaphore(new_concurrent)
                        logger.info(f"{provider}: 响应时间过长，调整并发数至 {new_concurrent}")

# 全局并发控制器实例
global_concurrency_controller = ConcurrencyController()
