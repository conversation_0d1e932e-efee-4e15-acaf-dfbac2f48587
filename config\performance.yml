# 御坂网络弹幕服务性能优化配置

# 数据库连接池配置
database_pool:
  minsize: 5                    # 最小连接数
  maxsize: 20                   # 最大连接数
  pool_recycle: 3600           # 连接回收时间（秒）
  connect_timeout: 10          # 连接超时（秒）
  
# 批量插入配置
batch_insert:
  batch_size: 1000             # 每批次最大弹幕数
  batch_timeout: 5.0           # 批次超时时间（秒）
  max_queue_size: 10000        # 最大队列大小
  chunk_size: 500              # 数据库单次插入块大小

# 任务管理器配置
task_manager:
  max_concurrent_tasks: 5      # 最大并发任务数
  task_timeout: 7200           # 任务超时时间（秒，2小时）
  
# CPU密集型任务配置
cpu_tasks:
  max_workers: 4               # 线程池最大工作线程数
  thread_name_prefix: "cpu-worker"

# 并发控制配置
concurrency_control:
  global_max_concurrent: 10    # 全局最大并发数
  
  # 各提供商速率限制配置
  providers:
    bilibili:
      requests_per_second: 2.0
      burst_size: 3
      max_concurrent: 3
      backoff_factor: 1.5
      max_backoff: 60.0
      recovery_factor: 0.9
      
    tencent:
      requests_per_second: 3.0
      burst_size: 5
      max_concurrent: 4
      backoff_factor: 1.3
      max_backoff: 45.0
      recovery_factor: 0.8
      
    iqiyi:
      requests_per_second: 2.5
      burst_size: 4
      max_concurrent: 3
      backoff_factor: 1.4
      max_backoff: 50.0
      recovery_factor: 0.85
      
    youku:
      requests_per_second: 2.0
      burst_size: 3
      max_concurrent: 3
      backoff_factor: 1.5
      max_backoff: 60.0
      recovery_factor: 0.9
      
    gamer:
      requests_per_second: 1.5
      burst_size: 2
      max_concurrent: 2
      backoff_factor: 1.6
      max_backoff: 90.0
      recovery_factor: 0.95
      
    default:
      requests_per_second: 1.0
      burst_size: 2
      max_concurrent: 2
      backoff_factor: 1.5
      max_backoff: 60.0
      recovery_factor: 0.9

# 性能监控配置
monitoring:
  stats_retention_size: 100    # 统计数据保留数量
  auto_optimization: true      # 是否启用自动优化
  optimization_interval: 300   # 自动优化间隔（秒）
  
# 内存管理配置
memory:
  max_comment_cache_size: 100000  # 最大弹幕缓存数量
  cache_cleanup_interval: 600    # 缓存清理间隔（秒）
  
# 数据库优化设置
database_optimization:
  # MySQL性能优化参数
  mysql_settings:
    innodb_buffer_pool_size: "128M"
    bulk_insert_buffer_size: "8M"
    innodb_log_buffer_size: "16M"
    innodb_flush_log_at_trx_commit: 2
    sync_binlog: 0
    innodb_lock_wait_timeout: 10
    
  # 索引优化建议
  recommended_indexes:
    - table: "comment"
      columns: ["episode_id", "t"]
      type: "BTREE"
      description: "优化按分集和时间查询弹幕"
    - table: "episode"
      columns: ["media_id", "episode_number"]
      type: "BTREE"
      description: "优化按媒体和集数查询"
    - table: "task_history"
      columns: ["status", "created_at"]
      type: "BTREE"
      description: "优化任务状态和时间查询"

# 系统资源限制
resource_limits:
  max_memory_usage: "2GB"      # 最大内存使用量
  max_cpu_usage: 80            # 最大CPU使用率（百分比）
  disk_space_threshold: "1GB"  # 磁盘空间阈值
  
# 告警配置
alerts:
  enable_alerts: true
  thresholds:
    high_cpu_usage: 85         # CPU使用率告警阈值
    high_memory_usage: 90      # 内存使用率告警阈值
    high_error_rate: 15        # 错误率告警阈值（百分比）
    slow_response_time: 10.0   # 慢响应时间告警阈值（秒）
    queue_size_warning: 8000   # 队列大小告警阈值
    
# 日志配置
logging:
  performance_log_level: "INFO"
  enable_performance_metrics: true
  metrics_log_interval: 60     # 性能指标日志间隔（秒）
  
# 缓存配置
caching:
  enable_response_cache: true
  cache_ttl: 300              # 缓存TTL（秒）
  max_cache_entries: 1000     # 最大缓存条目数
