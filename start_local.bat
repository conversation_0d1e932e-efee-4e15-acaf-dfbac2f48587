@echo off
chcp 65001 >nul
title LoveStory弹幕服务 - 本地启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                LoveStory弹幕服务 - 本地启动器                  ║
echo ║                LoveStory Danmu Server - Local Launcher       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.8或更高版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 🔍 检查Python版本...
python --version

REM 检查是否在项目目录中
if not exist "src\main.py" (
    echo ❌ 请在项目根目录中运行此脚本
    pause
    exit /b 1
)

echo.
echo 📦 准备启动环境...

REM 检查虚拟环境
if not exist "venv" (
    echo 🔧 创建虚拟环境...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
)

echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo 📥 安装/更新依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo ⚙️  检查配置文件...
if not exist "config\config.local.yml" (
    echo 📝 创建本地配置文件...
    copy "config\config.yml" "config\config.local.yml" >nul
    echo ✅ 已创建 config\config.local.yml
    echo ⚠️  请根据需要修改数据库配置
)

echo.
echo 📁 创建必要目录...
if not exist "config\logs" mkdir "config\logs"
if not exist "data" mkdir "data"
if not exist "temp" mkdir "temp"

echo.
echo 🚀 启动LoveStory弹幕服务...
echo ============================================================
echo 访问地址: http://127.0.0.1:7768
echo API文档: http://127.0.0.1:7768/docs
echo 按 Ctrl+C 停止服务
echo ============================================================
echo.

REM 设置环境变量
set CONFIG_FILE=config\config.local.yml

REM 启动服务
python start_local.py

echo.
echo 👋 服务已停止
pause
