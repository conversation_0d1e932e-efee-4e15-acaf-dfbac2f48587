#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoveStory弹幕服务安装脚本
自动检查环境并安装依赖
"""

import sys
import os
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        print(f"当前版本: {platform.python_version()}")
        return False
    print(f"✅ Python版本检查通过: {platform.python_version()}")
    return True

def install_requirements():
    """安装依赖包"""
    try:
        print("📦 开始安装依赖包...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def check_config():
    """检查配置文件"""
    config_file = "config/config.yml"
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    print(f"✅ 配置文件检查通过: {config_file}")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "config/logs",
        "static"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"📁 创建目录: {directory}")
        else:
            print(f"✅ 目录已存在: {directory}")

def main():
    """主安装函数"""
    print("=" * 60)
    print("🚀 LoveStory弹幕服务安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建必要目录
    create_directories()
    
    # 检查配置文件
    if not check_config():
        print("💡 请确保config/config.yml文件存在并配置正确")
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        sys.exit(1)
    
    print("=" * 60)
    print("🎉 安装完成！")
    print("=" * 60)
    print("📝 下一步操作:")
    print("1. 配置数据库连接 (编辑 config/config.yml)")
    print("2. 创建数据库和用户 (运行 init_database.sql)")
    print("3. 启动服务:")
    print("   - 宝塔部署: 在Python项目管理器中启动")
    print("   - 手动启动: python start.py")
    print("   - 或者: python app.py")
    print("=" * 60)

if __name__ == "__main__":
    main()
