# LoveStory弹幕服务
  

一个功能强大的自托管弹幕（Danmaku）聚合与管理服务，兼容 [dandanplay](https://api.dandanplay.net/swagger/index.html) API 规范。

本项目旨在通过刮削主流视频网站的弹幕，为您自己的媒体库提供一个统一、私有的弹幕API。它自带一个现代化的Web界面，方便您管理弹幕库、搜索源、API令牌和系统设置。

## ✨ 核心功能

- **多源刮削**: 自动从 Bilibili、腾讯视频、爱奇艺、优酷等多个来源获取弹幕。
- **智能匹配**: 通过文件名或元数据（TMDB, TVDB等）智能匹配您的影视文件，提供准确的弹幕。
- **Web管理界面**: 提供一个直观的Web UI，用于：
  - 搜索和手动导入弹幕。
  - 管理已收录的媒体库、数据源和分集。
  - 创建和管理供第三方客户端（如 yamby, hills, 小幻影视）使用的API令牌。
  - 配置搜索源的优先级和启用状态。
  - 查看后台任务进度和系统日志。
- **元数据整合**: 支持与 TMDB, TVDB, Bangumi, Douban, IMDb 集成，丰富您的媒体信息。
- **自动化**: 支持通过 Webhook 接收来自 Sonarr, Radarr, Emby 等服务的通知，实现全自动化的弹幕导入。
- **灵活部署**: 提供 Docker 镜像和 Docker Compose 文件，方便快速部署。

## 🚀 快速开始 (宝塔面板部署)

推荐使用宝塔面板进行部署，简单易用，适合个人和小型团队使用。

### 部署方式选择

1. **宝塔面板部署** (推荐) - 详见 `宝塔部署说明.md`
2. **手动部署** - 适合有经验的开发者

### 宝塔面板部署步骤

#### 1. 环境准备
- 安装宝塔面板 7.0+
- 安装Python 3.8+ (推荐3.11)
- 安装MySQL 8.0+
- 安装Python项目管理器

#### 2. 数据库配置

**本地MySQL数据库:**
```sql
-- 创建数据库和用户
CREATE DATABASE danmu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'danmu_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON danmu_db.* TO 'danmu_user'@'localhost';
FLUSH PRIVILEGES;
```

**远程数据库（推荐）:**
- 支持阿里云RDS、腾讯云CDB、AWS RDS等云数据库
- 自动TLS/SSL安全连接
- 配置向导：`python setup_ssl.py`
- 连接测试：`python test_db_connection.py`

#### 3. 项目部署
1. 上传项目文件到服务器
2. 在Python项目管理器中创建项目
3. 设置启动文件为 `app.py`
4. 安装依赖包 `requirements.txt`
5. 修改 `config/config.yml` 配置文件
6. 启动项目

#### 4. 访问配置
- **访问地址**: `http://您的服务器IP:7768`
- **初始用户**: `admin`
- **初始密码**: 查看项目日志获取
- **配置建议**: 登录后立即修改密码和JWT密钥

### 手动部署步骤

#### 1. 环境要求
- Python 3.8+
- MySQL 5.7+
- pip包管理器

#### 2. 安装依赖
```bash
pip install -r requirements.txt
```

#### 3. 配置数据库
- 创建MySQL数据库和用户
- 修改 `config/config.yml` 中的数据库配置

#### 4. 启动服务
```bash
python app.py
```

**详细部署说明请参考 `宝塔部署说明.md` 文件**

## 客户端配置

### 1. 获取弹幕 Token

- 在 Web UI 的 "弹幕Token" 页面，点击 "添加Token" 来创建一个新的访问令牌。
- 创建后，您会得到一串随机字符，这就是您的弹幕 Token。
- 可通过配置自定义域名之后直接点击复制，会帮你拼接好相关的链接

### 2. 配置弹幕接口

在您的播放器（如 Yamby, Hills, 小幻影视等）的自定义弹幕接口设置中，填入以下格式的地址：

`http://<服务器IP>:<端口>/api/<你的Token>`

-   `<服务器IP>`: 部署本服务的主机 IP 地址。
-   `<端口>`: 部署本服务时设置的端口（默认为 `7768`）。
-   `<你的Token>`: 您在上一步中创建的 Token 字符串。

**示例:**

假设您的服务部署在 `*************`，端口为 `7768`，创建的 Token 是 `Q2KHYcveM0SaRKvxomQm`。

-   **对于 Yamby / Hills:**
    在自定义弹幕接口中填写：
    `http://*************:7768/api/Q2KHYcveM0SaRKvxomQm`

-   **对于 小幻影视:**
    小幻影视可能需要一个包含 `/api/v2` 的路径，您可以填写：
    `http://*************:7768/api/Q2KHYcveM0SaRKvxomQm/api/v2`

> **兼容性说明**: 本服务已对路由进行特殊处理，无论您使用 `.../api/<Token>` 还是 `.../api/<Token>/api/v2` 格式，服务都能正确响应，以最大程度兼容不同客户端。


## Webhook 配置 

本服务支持通过 Webhook 接收来自 Emby 等媒体服务器的通知，实现新媒体入库后的弹幕自动搜索和导入。

### 1. 获取 Webhook URL

1.  在 Web UI 的 "设置" -> "Webhook" 页面，您会看到一个为您生成的唯一的 **API Key**。
2.  根据您要集成的服务，复制对应的 Webhook URL。URL 的通用格式为：
    `http://<服务器IP>:<端口>/api/webhook/{服务名}?api_key=<你的API_Key>`

    -   `<服务器IP>`: 部署本服务的主机 IP 地址。
    -   `<端口>`: 部署本服务时设置的端口（默认为 `7768`）。
    -   `{服务名}`: webhook界面中下方已加载的服务名称，例如 `emby`。
    -   `<你的API_Key>`: 您在 Webhook 设置页面获取的密钥。

### 2. 配置 Emby

在 Emby 中设置 Webhook 以在媒体入库时通知本服务。

1.  登录您的 Emby 服务器管理后台。
2.  导航到 **通知** (Notifications)。
3.  点击 **添加通知** (Add Notification)，选择 **Webhook** 类型。
4.  在 **Webhook URL** 字段中，填入您的 Emby Webhook URL，例如：
    ```
    http://*************:7768/api/webhook/emby?api_key=your_webhook_api_key_here
    ```
5.  **关键步骤**: 在 **事件** (Events) 部分，请务必**只勾选**以下事件：
    -   **项目已添加 (Item Added)**: 这是新媒体入库的事件，其对应的事件名为 `新媒体添加`。
6.  确保 **发送内容类型** (Content type) 设置为 `application/json`。
7.  保存设置。

现在，当有新的电影或剧集添加到您的 Emby 媒体库时，本服务将自动收到通知，并创建一个后台任务来为其搜索和导入弹幕。

