.source-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.source-controls h2 {
    margin: 0;
    padding: 0;
    border: none;
    font-size: 1.5em;
}

.source-actions button {
    margin-left: 10px;
    background-color: #6c757d;
}

#save-danmaku-sources-btn,
#save-metadata-sources-btn {
    background-color: var(--success-color);
}

#danmaku-sources-list,
#metadata-sources-list {
    list-style: none;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

#danmaku-sources-list li,
#metadata-sources-list li {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s;
    gap: 10px;
}

#danmaku-sources-list li:last-child,
#metadata-sources-list li:last-child {
    border-bottom: none;
}

.source-name {
    flex-grow: 1;
}

.config-btn {
    margin-left: auto;
    flex-shrink: 0;
}

#danmaku-sources-list li:hover,
#metadata-sources-list li:hover {
    background-color: #f8f9fa;
}

#danmaku-sources-list li.selected,
#metadata-sources-list li.selected {
    background-color: #e9ecef;
    border-left: 3px solid var(--primary-color);
}

.status-icon {
    font-size: 1.2em;
    padding: 3px 8px;
    border-radius: 12px;
    background-color: #e9ecef;
    flex-shrink: 0;
}
