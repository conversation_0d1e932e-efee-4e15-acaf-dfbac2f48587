# 🚀 LoveStory弹幕服务 - 快速启动指南

## 一键启动

### Windows 用户
双击运行 `start_local.bat` 文件，或在命令行中执行：
```cmd
start_local.bat
```

### Linux/macOS 用户
在终端中执行：
```bash
chmod +x start_local.sh
./start_local.sh
```

### 或使用Python脚本
```bash
python start_local.py
```

## 启动后访问

- **Web界面**: http://127.0.0.1:7768
- **API文档**: http://127.0.0.1:7768/docs
- **默认账户**: admin / admin

## 数据库配置选项

### 选项1: 使用现有TiDB Cloud（推荐）
项目已配置好TiDB Cloud连接，可直接使用，无需额外配置。

### 选项2: 使用本地MySQL
1. 安装MySQL 8.0+
2. 创建数据库：
```sql
CREATE DATABASE danmuapi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'danmuapi'@'localhost' IDENTIFIED BY 'danmuapi';
GRANT ALL PRIVILEGES ON danmuapi.* TO 'danmuapi'@'localhost';
FLUSH PRIVILEGES;
```
3. 修改 `config/config.local.yml` 中的数据库配置

## 常见问题

### 端口被占用
修改 `config/config.local.yml` 中的端口号：
```yaml
server:
  port: 8080  # 改为其他端口
```

### Python版本问题
确保使用Python 3.8或更高版本：
```bash
python --version
```

### 依赖安装失败
使用国内镜像源：
```bash
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 功能测试

1. 登录Web界面 (admin/admin)
2. 搜索影视作品，如"鬼灭之刃"
3. 选择匹配结果并导入弹幕
4. 查看任务管理器中的执行进度

## 停止服务

按 `Ctrl+C` 停止服务

---

**需要帮助？** 查看完整的 [本地部署指南](LOCAL_DEPLOYMENT_GUIDE.md)
