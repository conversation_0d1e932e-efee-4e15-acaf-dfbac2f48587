"""
性能优化管理器
提供批量数据处理、CPU密集型任务异步化、内存缓冲等性能优化功能
"""

import asyncio
import logging
import time
from collections import deque
from typing import Any, Callable, Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import aiomysql
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

@dataclass
class BatchInsertItem:
    """批量插入项目"""
    episode_id: int
    comments: List[Dict[str, Any]]
    callback: Optional[Callable] = None

class PerformanceManager:
    """性能优化管理器"""
    
    def __init__(self, pool: aiomysql.Pool):
        self.pool = pool
        self._batch_queue = asyncio.Queue()
        self._batch_worker_task: Optional[asyncio.Task] = None
        self._cpu_executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="cpu-worker")
        self._shutdown_event = asyncio.Event()
        
        # 批量插入配置
        self.batch_size = 1000  # 每批次最大弹幕数
        self.batch_timeout = 5.0  # 批次超时时间（秒）
        self.max_queue_size = 10000  # 最大队列大小
        
        # 性能统计
        self._stats = {
            'total_comments_processed': 0,
            'total_batches_processed': 0,
            'avg_batch_time': 0.0,
            'last_batch_time': None
        }

    async def start(self):
        """启动性能管理器"""
        if self._batch_worker_task is None:
            self._batch_worker_task = asyncio.create_task(self._batch_worker())
            logger.info("性能管理器已启动")

    async def stop(self):
        """停止性能管理器"""
        self._shutdown_event.set()
        
        if self._batch_worker_task:
            self._batch_worker_task.cancel()
            try:
                await self._batch_worker_task
            except asyncio.CancelledError:
                pass
            self._batch_worker_task = None
        
        # 关闭线程池
        self._cpu_executor.shutdown(wait=True)
        logger.info("性能管理器已停止")

    async def bulk_insert_comments_async(
        self, 
        episode_id: int, 
        comments: List[Dict[str, Any]], 
        callback: Optional[Callable] = None
    ) -> int:
        """异步批量插入弹幕，使用队列缓冲"""
        if not comments:
            return 0
        
        # 检查队列大小，防止内存溢出
        if self._batch_queue.qsize() > self.max_queue_size:
            logger.warning(f"批量插入队列已满 ({self.max_queue_size})，直接执行插入")
            return await self._direct_bulk_insert(episode_id, comments)
        
        # 将任务加入队列
        item = BatchInsertItem(episode_id, comments, callback)
        await self._batch_queue.put(item)
        
        return len(comments)  # 返回预期插入数量

    async def _batch_worker(self):
        """批量处理工作协程"""
        logger.info("批量插入工作协程已启动")
        
        batch_buffer = []
        last_flush_time = time.time()
        
        while not self._shutdown_event.is_set():
            try:
                # 等待新项目或超时
                try:
                    item = await asyncio.wait_for(
                        self._batch_queue.get(), 
                        timeout=1.0
                    )
                    batch_buffer.append(item)
                except asyncio.TimeoutError:
                    pass
                
                current_time = time.time()
                should_flush = (
                    len(batch_buffer) >= self.batch_size or
                    (batch_buffer and current_time - last_flush_time >= self.batch_timeout)
                )
                
                if should_flush and batch_buffer:
                    await self._process_batch(batch_buffer)
                    batch_buffer.clear()
                    last_flush_time = current_time
                    
            except Exception as e:
                logger.error(f"批量处理工作协程出错: {e}", exc_info=True)
        
        # 处理剩余的批次
        if batch_buffer:
            await self._process_batch(batch_buffer)
        
        logger.info("批量插入工作协程已停止")

    async def _process_batch(self, batch_buffer: List[BatchInsertItem]):
        """处理一个批次的插入操作"""
        start_time = time.time()
        total_comments = sum(len(item.comments) for item in batch_buffer)
        
        logger.debug(f"开始处理批次: {len(batch_buffer)} 个任务, {total_comments} 条弹幕")
        
        try:
            # 按episode_id分组处理
            episode_groups = {}
            for item in batch_buffer:
                if item.episode_id not in episode_groups:
                    episode_groups[item.episode_id] = []
                episode_groups[item.episode_id].extend(item.comments)
            
            # 并发处理各个分集
            tasks = [
                self._direct_bulk_insert(episode_id, comments)
                for episode_id, comments in episode_groups.items()
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计结果
            total_inserted = 0
            for result in results:
                if isinstance(result, int):
                    total_inserted += result
                else:
                    logger.error(f"批次处理中出现错误: {result}")
            
            # 执行回调
            for item in batch_buffer:
                if item.callback:
                    try:
                        await item.callback(len(item.comments))
                    except Exception as e:
                        logger.error(f"批次回调执行失败: {e}")
            
            # 更新统计信息
            batch_time = time.time() - start_time
            self._update_stats(total_inserted, 1, batch_time)
            
            logger.debug(f"批次处理完成: 插入 {total_inserted} 条弹幕, 耗时 {batch_time:.2f}s")
            
        except Exception as e:
            logger.error(f"批次处理失败: {e}", exc_info=True)

    async def _direct_bulk_insert(self, episode_id: int, comments: List[Dict[str, Any]]) -> int:
        """直接批量插入弹幕（优化版本）"""
        if not comments:
            return 0
        
        # 分批插入，避免单次插入过多数据
        chunk_size = 500  # 每次插入500条
        total_inserted = 0
        
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                try:
                    # 开始事务
                    await conn.begin()
                    
                    for i in range(0, len(comments), chunk_size):
                        chunk = comments[i:i + chunk_size]
                        
                        # 使用优化的批量插入语句
                        query = """
                        INSERT IGNORE INTO comment (episode_id, cid, p, m, t) 
                        VALUES (%s, %s, %s, %s, %s)
                        """
                        
                        data_to_insert = [
                            (episode_id, c['cid'], c['p'], c['m'], c['t']) 
                            for c in chunk
                        ]
                        
                        affected_rows = await cursor.executemany(query, data_to_insert)
                        total_inserted += affected_rows
                    
                    # 更新分集弹幕计数
                    if total_inserted > 0:
                        await cursor.execute(
                            "UPDATE episode SET comment_count = comment_count + %s WHERE id = %s", 
                            (total_inserted, episode_id)
                        )
                    
                    await conn.commit()
                    
                except Exception as e:
                    await conn.rollback()
                    logger.error(f"批量插入失败 (episode_id={episode_id}): {e}")
                    raise
        
        return total_inserted

    async def cpu_intensive_task(self, func: Callable, *args, **kwargs) -> Any:
        """在线程池中执行CPU密集型任务"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._cpu_executor, func, *args, **kwargs)

    def _update_stats(self, comments_count: int, batches_count: int, batch_time: float):
        """更新性能统计信息"""
        self._stats['total_comments_processed'] += comments_count
        self._stats['total_batches_processed'] += batches_count
        
        # 计算平均批次时间
        total_batches = self._stats['total_batches_processed']
        if total_batches > 1:
            self._stats['avg_batch_time'] = (
                (self._stats['avg_batch_time'] * (total_batches - 1) + batch_time) / total_batches
            )
        else:
            self._stats['avg_batch_time'] = batch_time
        
        self._stats['last_batch_time'] = datetime.now()

    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        return self._stats.copy()

    async def optimize_database_settings(self):
        """优化数据库设置"""
        optimizations = [
            "SET SESSION innodb_buffer_pool_size = 128M",
            "SET SESSION bulk_insert_buffer_size = 8M",
            "SET SESSION innodb_log_buffer_size = 16M",
            "SET SESSION innodb_flush_log_at_trx_commit = 2",
            "SET SESSION sync_binlog = 0"
        ]
        
        async with self.pool.acquire() as conn:
            async with conn.cursor() as cursor:
                for sql in optimizations:
                    try:
                        await cursor.execute(sql)
                        logger.debug(f"数据库优化设置: {sql}")
                    except Exception as e:
                        logger.warning(f"数据库优化设置失败 ({sql}): {e}")
