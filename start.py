#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoveStory弹幕服务启动脚本
用于手动部署或开发环境
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def main():
    """主启动函数"""
    try:
        # 导入配置和应用
        from src.config import settings
        from src.main import app
        
        print("=" * 60)
        print("🎯 LoveStory弹幕服务启动中...")
        print("=" * 60)
        print(f"📍 服务地址: http://{settings.server.host}:{settings.server.port}")
        print(f"🗄️  数据库: {settings.database.host}:{settings.database.port}/{settings.database.name}")
        print(f"📁 项目路径: {project_root}")
        print("=" * 60)
        
        # 启动服务
        import uvicorn
        uvicorn.run(
            app,
            host=settings.server.host,
            port=settings.server.port,
            reload=False,
            access_log=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖包: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
