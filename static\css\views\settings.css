.settings-sub-nav {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.sub-nav-btn {
    padding: 10px 20px;
    border: 1px solid transparent;
    border-bottom: none;
    background-color: transparent;
    color: #666;
    cursor: pointer;
    border-radius: 4px 4px 0 0;
    font-size: 15px;
    font-weight: 500;
    position: relative;
    top: 1px;
    transition: all 0.2s ease;
}

.sub-nav-btn:hover {
    background-color: var(--secondary-color);
    color: var(--primary-color);
}

.sub-nav-btn.active {
    background-color: var(--bg-color);
    color: var(--primary-color);
    border-color: var(--border-color);
}

.bangumi-user-profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

textarea#douban-cookie {
    width: 100%;
}

/* 让只读输入框看起来更像普通文本 */
#webhook-settings-subview input[readonly] {
    background-color: var(--secondary-color);
}

#webhook-handlers-list-container {
    margin-top: 20px;
}

#webhook-handlers-list-container h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1em;
    color: #606266;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

#webhook-handlers-list {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

#webhook-handlers-list li {
    background-color: var(--secondary-color);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
}
