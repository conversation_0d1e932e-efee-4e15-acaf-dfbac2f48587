import asyncio
import aiomysql
import logging
import traceback
from enum import Enum
from typing import Any, Callable, Coroutine, Dict, List, Tuple, Optional
from uuid import uuid4
from datetime import datetime, timedelta

from . import models, crud

logger = logging.getLogger(__name__)

class TaskStatus(str, Enum):
    PENDING = "排队中"
    RUNNING = "运行中"
    COMPLETED = "已完成"
    FAILED = "失败"
    CANCELLED = "已取消"

class TaskSuccess(Exception):
    """自定义异常，用于表示任务成功完成并附带一条最终消息。"""
    pass

class TaskTimeout(Exception):
    """自定义异常，用于表示任务超时。"""
    pass

class Task:
    def __init__(self, task_id: str, title: str, coro_factory: Callable[[Callable], Coroutine]):
        self.task_id = task_id
        self.title = title
        self.coro_factory = coro_factory
        self.done_event = asyncio.Event()
        self.cancel_event = asyncio.Event()
        self.running_task: Optional[asyncio.Task] = None
        self.created_at = datetime.now()

class TaskManager:
    def __init__(self, pool: aiomysql.Pool, max_concurrent_tasks: int = 3, task_timeout: int = 3600):
        self._pool = pool
        self._queue: asyncio.Queue = asyncio.Queue()
        self._worker_tasks: List[asyncio.Task] = []
        self._running_tasks: Dict[str, Task] = {}
        self._max_concurrent_tasks = max_concurrent_tasks
        self._task_timeout = task_timeout  # 默认1小时超时
        self._shutdown_event = asyncio.Event()

    def start(self):
        """启动后台工作协程来处理任务队列。"""
        if not self._worker_tasks:
            # 启动多个worker协程以支持并发任务执行
            for i in range(self._max_concurrent_tasks):
                worker_task = asyncio.create_task(self._worker(f"worker-{i}"))
                self._worker_tasks.append(worker_task)

            # 启动任务监控协程
            monitor_task = asyncio.create_task(self._task_monitor())
            self._worker_tasks.append(monitor_task)

            logger.info(f"任务管理器已启动，{self._max_concurrent_tasks}个工作协程，任务超时: {self._task_timeout}秒")

    async def stop(self):
        """停止任务管理器。"""
        if self._worker_tasks:
            # 设置关闭事件
            self._shutdown_event.set()

            # 取消所有正在运行的任务
            for task in self._running_tasks.values():
                if task.running_task and not task.running_task.done():
                    task.cancel_event.set()
                    task.running_task.cancel()

            # 取消所有worker协程
            for worker_task in self._worker_tasks:
                worker_task.cancel()

            # 等待所有worker协程结束
            try:
                await asyncio.gather(*self._worker_tasks, return_exceptions=True)
            except Exception:
                pass

            self._worker_tasks.clear()
            self._running_tasks.clear()
            logger.info("任务管理器已停止。")

    async def _worker(self, worker_name: str):
        """从队列中获取并执行任务。"""
        logger.info(f"任务工作协程 {worker_name} 已启动")

        while not self._shutdown_event.is_set():
            try:
                # 使用超时等待任务，避免无限阻塞
                task: Task = await asyncio.wait_for(self._queue.get(), timeout=1.0)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"工作协程 {worker_name} 获取任务时出错: {e}")
                continue

            # 将任务添加到运行中的任务字典
            self._running_tasks[task.task_id] = task

            logger.info(f"[{worker_name}] 开始执行任务 '{task.title}' (ID: {task.task_id})")

            await crud.update_task_progress_in_history(
                self._pool, task.task_id, TaskStatus.RUNNING, 0, "正在初始化..."
            )

            try:
                # 创建一个回调函数，该函数将由任务内部调用以更新其进度
                progress_callback = self.get_progress_callback(task.task_id)
                # task.coro_factory 是一个需要回调函数作为参数的 lambda
                # 调用它以获取真正的、可等待的协程
                actual_coroutine = task.coro_factory(progress_callback)

                # 使用超时和取消事件来执行任务
                task.running_task = asyncio.create_task(actual_coroutine)

                # 等待任务完成、超时或取消
                done, pending = await asyncio.wait(
                    [task.running_task, asyncio.create_task(task.cancel_event.wait())],
                    return_when=asyncio.FIRST_COMPLETED,
                    timeout=self._task_timeout
                )

                # 取消未完成的任务
                for p in pending:
                    p.cancel()

                if task.cancel_event.is_set():
                    # 任务被取消
                    await crud.finalize_task_in_history(
                        self._pool, task.task_id, TaskStatus.CANCELLED, "任务已被取消"
                    )
                    logger.info(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 已被取消")
                elif not done:
                    # 任务超时
                    task.running_task.cancel()
                    await crud.finalize_task_in_history(
                        self._pool, task.task_id, TaskStatus.FAILED, f"任务超时 ({self._task_timeout}秒)"
                    )
                    logger.error(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 执行超时")
                else:
                    # 任务正常完成，检查结果
                    try:
                        await task.running_task
                        # 对于没有引发 TaskSuccess 异常而正常结束的任务，使用通用成功消息
                        await crud.finalize_task_in_history(
                            self._pool, task.task_id, TaskStatus.COMPLETED, "任务成功完成"
                        )
                        logger.info(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 已成功完成")
                    except TaskSuccess as e:
                        # 捕获 TaskSuccess 异常，使用其消息作为最终描述
                        final_message = str(e) if str(e) else "任务成功完成"
                        await crud.finalize_task_in_history(
                            self._pool, task.task_id, TaskStatus.COMPLETED, final_message
                        )
                        logger.info(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 已成功完成，消息: {final_message}")
                    except Exception as e:
                        error_message = f"任务执行失败: {str(e)}"
                        await crud.finalize_task_in_history(
                            self._pool, task.task_id, TaskStatus.FAILED, error_message
                        )
                        logger.error(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 执行失败: {traceback.format_exc()}")

            except Exception as e:
                error_message = f"任务执行异常: {str(e)}"
                await crud.finalize_task_in_history(
                    self._pool, task.task_id, TaskStatus.FAILED, error_message
                )
                logger.error(f"[{worker_name}] 任务 '{task.title}' (ID: {task.task_id}) 执行异常: {traceback.format_exc()}")
            finally:
                # 从运行中的任务字典中移除
                self._running_tasks.pop(task.task_id, None)
                self._queue.task_done()
                task.done_event.set()

        logger.info(f"任务工作协程 {worker_name} 已停止")

    async def _task_monitor(self):
        """监控任务状态，处理超时和清理工作。"""
        logger.info("任务监控协程已启动")

        while not self._shutdown_event.is_set():
            try:
                await asyncio.sleep(30)  # 每30秒检查一次

                # 检查是否有超时的任务
                current_time = datetime.now()
                for task_id, task in list(self._running_tasks.items()):
                    if current_time - task.created_at > timedelta(seconds=self._task_timeout):
                        logger.warning(f"检测到超时任务: {task.title} (ID: {task_id})")
                        task.cancel_event.set()

            except Exception as e:
                logger.error(f"任务监控协程出错: {e}")

        logger.info("任务监控协程已停止")

    async def submit_task(self, coro_factory: Callable[[Callable], Coroutine], title: str) -> Tuple[str, asyncio.Event]:
        """提交一个新任务到队列，并在数据库中创建记录。返回任务ID和完成事件。"""
        task_id = str(uuid4())
        task = Task(task_id, title, coro_factory)
        
        await crud.create_task_in_history(
            self._pool, task_id, title, TaskStatus.PENDING, "等待执行..."
        )
        
        await self._queue.put(task)
        logger.info(f"任务 '{title}' 已提交，ID: {task_id}")
        return task_id, task.done_event

    def get_progress_callback(self, task_id: str) -> Callable:
        """为特定任务创建一个回调闭包。"""
        def callback(progress: int, description: str):
            # 检查任务是否被取消
            if task_id in self._running_tasks and self._running_tasks[task_id].cancel_event.is_set():
                raise asyncio.CancelledError("任务已被取消")

            # 这是一个“即发即忘”的调用，以避免阻塞正在运行的任务
            asyncio.create_task(
                crud.update_task_progress_in_history(
                    self._pool, task_id, TaskStatus.RUNNING, int(progress), description
                )
            )
        return callback

    async def cancel_task(self, task_id: str) -> bool:
        """取消一个正在运行的任务。"""
        if task_id in self._running_tasks:
            task = self._running_tasks[task_id]
            task.cancel_event.set()
            if task.running_task and not task.running_task.done():
                task.running_task.cancel()
            logger.info(f"任务 '{task.title}' (ID: {task_id}) 已被标记为取消")
            return True
        return False

    def get_running_tasks(self) -> Dict[str, str]:
        """获取当前正在运行的任务列表。"""
        return {task_id: task.title for task_id, task in self._running_tasks.items()}