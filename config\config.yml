# 服务器配置
server:
  host: "0.0.0.0"  # 监听所有接口，适合宝塔部署
  port: 7768

# 数据库配置 - 本地MySQL数据库（适合宝塔部署）
database:
  host: "127.0.0.1"  # 本地MySQL服务器
  port: 3306
  user: "danmu_user"  # 请修改为您的数据库用户名
  password: "your_password_here"  # 请修改为您的数据库密码
  name: "danmu_db"  # 数据库名称
  # TLS/SSL配置（本地MySQL通常不需要SSL）
  ssl_enabled: false  # 本地连接不启用SSL
  ssl_verify_cert: false
  ssl_ca_path: null
  ssl_cert_path: null
  ssl_key_path: null

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "oyrgqpig680t87gFOUVPWBPF6587IUF8futcfytdiyd64e" # 请务必修改为一个复杂且唯一的密钥, 建议通过环境变量覆盖
  algorithm: "HS256"
  access_token_expire_minutes: 1440 # 令牌有效期（分钟），例如 1天
