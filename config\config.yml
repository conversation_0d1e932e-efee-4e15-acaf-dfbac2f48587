# 服务器配置
server:
  host: "0.0.0.0"  # 监听所有接口，适合宝塔部署
  port: 7768

# 数据库配置 - 远程MySQL数据库（支持TLS安全连接）
database:
  host: "your-remote-db-host.com"  # 远程数据库服务器地址
  port: 3306  # 数据库端口，通常为3306
  user: "your_db_username"  # 请修改为您的数据库用户名
  password: "your_db_password"  # 请修改为您的数据库密码
  name: "your_db_name"  # 数据库名称

  # TLS/SSL安全连接配置
  ssl_enabled: true  # 启用SSL/TLS连接（远程数据库强烈推荐）
  ssl_verify_cert: true  # 验证服务器证书（推荐开启以提高安全性）
  ssl_ca_path: "config/ssl/ca-cert.pem"  # CA证书文件路径（可选）
  ssl_cert_path: "config/ssl/client-cert.pem"  # 客户端证书文件路径（可选）
  ssl_key_path: "config/ssl/client-key.pem"  # 客户端私钥文件路径（可选）

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "oyrgqpig680t87gFOUVPWBPF6587IUF8futcfytdiyd64e" # 请务必修改为一个复杂且唯一的密钥, 建议通过环境变量覆盖
  algorithm: "HS256"
  access_token_expire_minutes: 1440 # 令牌有效期（分钟），例如 1天
