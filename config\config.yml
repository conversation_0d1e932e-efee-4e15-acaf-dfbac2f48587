# 服务器配置
server:
  host: "127.0.0.1"  # 本地访问
  port: 7768

# 数据库配置 - 使用TiDB Cloud（已配置好，可直接使用）
database:
  host: "gateway01.ap-southeast-1.prod.aws.tidbcloud.com"
  port: 4000
  user: "2bmga6guXNxtZeu.root"
  password: "o61AeN7aExII4DDs" # 请修改为您的数据库密码, 建议通过环境变量覆盖
  name: "test"
  # TLS/SSL配置
  ssl_enabled: true  # 启用SSL/TLS连接（TiDB Cloud需要）
  ssl_verify_cert: false  # 是否验证服务器证书（可设为true以提高安全性）
  ssl_ca_path: null  # CA证书文件路径（可选，用于自定义CA）
  ssl_cert_path: null  # 客户端证书文件路径（可选）
  ssl_key_path: null  # 客户端私钥文件路径（可选）

# JWT (JSON Web Token) 配置
jwt:
  secret_key: "oyrgqpig680t87gFOUVPWBPF6587IUF8futcfytdiyd64e" # 请务必修改为一个复杂且唯一的密钥, 建议通过环境变量覆盖
  algorithm: "HS256"
  access_token_expire_minutes: 1440 # 令牌有效期（分钟），例如 1天
