import logging
import re
from typing import Any, Callable, Optional

import aiomysql
from thefuzz import fuzz

from .. import crud
from ..api.ui import generic_import_task, parse_search_keyword
from ..scraper_manager import <PERSON>raperManager
from ..task_manager import TaskManager, TaskSuccess

logger = logging.getLogger(__name__)


def _is_movie_by_title(title: str) -> bool:
    """
    通过标题中的关键词（如“剧场版”）判断是否为电影。
    """
    if not title:
        return False
    # 关键词列表，不区分大小写
    movie_keywords = ["剧场版", "劇場版", "movie", "映画"]
    title_lower = title.lower()
    return any(keyword in title_lower for keyword in movie_keywords)


async def webhook_search_and_dispatch_task(
    anime_title: str,
    media_type: str,
    season: int,
    current_episode_index: int,
    search_keyword: str,
    douban_id: Optional[str],
    tmdb_id: Optional[str],
    imdb_id: Optional[str],
    tvdb_id: Optional[str],
    progress_callback: Callable,
    pool: aiomysql.Pool,
    manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    task_manager: TaskManager,
):
    """
    Webhook 触发的后台任务：搜索所有源，找到最佳匹配，并为该匹配分发一个新的、具体的导入任务。
    """
    try:
        logger.info(f"Webhook 任务: 开始为 '{anime_title}' (S{season:02d}E{current_episode_index:02d}) 查找最佳源...")
        progress_callback(5, "正在检查已收藏的源...")

        # 1. 优先查找已收藏的源
        favorited_source = await crud.find_favorited_source_for_anime(pool, anime_title, season)
        if favorited_source:
            logger.info(f"Webhook 任务: 找到已收藏的源 '{favorited_source['provider_name']}'，将直接使用此源。")
            progress_callback(10, f"找到已收藏的源: {favorited_source['provider_name']}")

            # 直接使用这个源的信息创建导入任务
            task_title = f"Webhook自动导入: {favorited_source['anime_title']} ({favorited_source['provider_name']})"
            task_coro = lambda cb: generic_import_task(
                provider=favorited_source['provider_name'], media_id=favorited_source['media_id'],
                anime_title=favorited_source['anime_title'], media_type=favorited_source['media_type'],
                season=season, current_episode_index=current_episode_index,
                image_url=favorited_source['image_url'], douban_id=douban_id,
                tmdb_id=tmdb_id, imdb_id=imdb_id, tvdb_id=tvdb_id,
                progress_callback=cb, pool=pool, manager=manager,
                task_manager=task_manager
            )
            await task_manager.submit_task(task_coro, task_title)
            raise TaskSuccess(f"Webhook: 已为收藏源 '{favorited_source['provider_name']}' 创建导入任务。")

        # 2. 如果没有收藏源，则并发搜索所有启用的源
        logger.info(f"Webhook 任务: 未找到收藏源，开始并发搜索所有启用的源...")
        progress_callback(20, "并发搜索所有源...")

        # 关键修复：像UI一样，先解析搜索关键词，分离出纯标题
        parsed_keyword = parse_search_keyword(search_keyword)
        search_title_only = parsed_keyword["title"]
        logger.info(f"Webhook 任务: 已将搜索词 '{search_keyword}' 解析为标题 '{search_title_only}' 进行搜索。")

        all_search_results = await manager.search_all(
            [search_title_only], episode_info={"season": season, "episode": current_episode_index}
        )

        if not all_search_results:
            raise TaskSuccess(f"Webhook 任务失败: 未找到 '{anime_title}' 的任何可用源。")

        # 3. 从所有源的返回结果中，根据类型、季度和标题相似度选择最佳匹配项
        ordered_settings = await crud.get_all_scraper_settings(pool)
        provider_order = {s['provider_name']: s['display_order'] for s in ordered_settings}

        valid_candidates = []
        for item in all_search_results:
            if item.type == 'tv_series' and _is_movie_by_title(item.title):
                item.type = 'movie'
                item.season = 1

            type_match = (item.type == media_type)
            season_match = (item.season == season) if media_type == 'tv_series' else True

            if type_match and season_match:
                valid_candidates.append(item)

        if not valid_candidates:
            raise TaskSuccess(f"Webhook 任务失败: 未找到 '{anime_title}' 的精确匹配项。")

        valid_candidates.sort(
            key=lambda item: (fuzz.token_set_ratio(anime_title, item.title), -provider_order.get(item.provider, 999)),
            reverse=True
        )
        best_match = valid_candidates[0]

        logger.info(f"Webhook 任务: 在所有源中找到最佳匹配项 '{best_match.title}' (来自: {best_match.provider})，将为其创建导入任务。")
        progress_callback(50, f"在 {best_match.provider} 中找到最佳匹配项")

        task_title = f"Webhook自动导入: {best_match.title} ({best_match.provider})"
        task_coro = lambda cb: generic_import_task(
            provider=best_match.provider, media_id=best_match.mediaId,
            anime_title=best_match.title, media_type=best_match.type,
            season=season, current_episode_index=best_match.currentEpisodeIndex,
            image_url=best_match.imageUrl, douban_id=douban_id,
            tmdb_id=tmdb_id, imdb_id=imdb_id, tvdb_id=tvdb_id,
            progress_callback=cb, pool=pool, manager=manager,
            task_manager=task_manager
        )
        await task_manager.submit_task(task_coro, task_title)
        raise TaskSuccess(f"Webhook: 已为源 '{best_match.provider}' 创建导入任务。")
    except TaskSuccess:
        raise
    except Exception as e:
        logger.error(f"Webhook 搜索与分发任务发生严重错误: {e}", exc_info=True)
        raise