#!/usr/bin/env python3
"""
性能测试脚本
用于测试优化后的弹幕服务性能
"""

import asyncio
import time
import random
import logging
from typing import List, Dict, Any
import aiomysql
from src.config import settings
from src.performance_manager import PerformanceManager
from src.concurrency_controller import global_concurrency_controller

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_test_pool():
    """创建测试数据库连接池"""
    return await aiomysql.create_pool(
        host=settings.database.host,
        port=settings.database.port,
        user=settings.database.user,
        password=settings.database.password,
        db=settings.database.name,
        autocommit=True,
        minsize=5,
        maxsize=20
    )

def generate_test_comments(count: int, episode_id: int = 1) -> List[Dict[str, Any]]:
    """生成测试弹幕数据"""
    comments = []
    for i in range(count):
        comments.append({
            'cid': f'test_{episode_id}_{i}_{random.randint(1000, 9999)}',
            'p': f'{random.uniform(0, 3600):.2f},1,16777215,{random.randint(1000000, 9999999)}',
            'm': f'测试弹幕内容 {i} - {random.choice(["哈哈哈", "666", "好看", "精彩", "不错"])}',
            't': random.uniform(0, 3600)
        })
    return comments

async def test_batch_insert_performance():
    """测试批量插入性能"""
    logger.info("=== 批量插入性能测试 ===")
    
    pool = await create_test_pool()
    performance_manager = PerformanceManager(pool)
    await performance_manager.start()
    
    try:
        # 测试不同批次大小的性能
        batch_sizes = [100, 500, 1000, 2000]
        results = {}
        
        for batch_size in batch_sizes:
            logger.info(f"测试批次大小: {batch_size}")
            
            # 生成测试数据
            test_comments = generate_test_comments(batch_size)
            
            # 测试传统方法
            start_time = time.time()
            from src import crud
            traditional_count = await crud.bulk_insert_comments(pool, 1, test_comments)
            traditional_time = time.time() - start_time
            
            # 清理数据
            async with pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("DELETE FROM comment WHERE episode_id = 1")
            
            # 测试优化方法
            start_time = time.time()
            optimized_count = await performance_manager.bulk_insert_comments_async(1, test_comments)
            optimized_time = time.time() - start_time
            
            # 等待异步处理完成
            await asyncio.sleep(2)
            
            results[batch_size] = {
                'traditional_time': traditional_time,
                'traditional_count': traditional_count,
                'optimized_time': optimized_time,
                'optimized_count': optimized_count,
                'improvement': (traditional_time - optimized_time) / traditional_time * 100
            }
            
            logger.info(f"批次 {batch_size}: 传统方法 {traditional_time:.2f}s, 优化方法 {optimized_time:.2f}s, 改进 {results[batch_size]['improvement']:.1f}%")
        
        return results
        
    finally:
        await performance_manager.stop()
        pool.close()
        await pool.wait_closed()

async def test_concurrency_control():
    """测试并发控制性能"""
    logger.info("=== 并发控制性能测试 ===")
    
    async def mock_request(provider: str, delay: float = 0.1):
        """模拟HTTP请求"""
        await asyncio.sleep(delay + random.uniform(0, 0.05))
        if random.random() < 0.1:  # 10%概率出错
            raise Exception("模拟请求错误")
        return f"成功响应 from {provider}"
    
    # 测试不同提供商的并发控制
    providers = ['bilibili', 'tencent', 'iqiyi', 'youku']
    tasks = []
    
    start_time = time.time()
    
    # 创建大量并发请求
    for _ in range(50):
        provider = random.choice(providers)
        task = global_concurrency_controller.execute_with_control(
            provider, mock_request, provider, random.uniform(0.1, 0.5)
        )
        tasks.append(task)
    
    # 执行所有任务
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    total_time = time.time() - start_time
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    error_count = len(results) - success_count
    
    logger.info(f"并发测试完成: {total_time:.2f}s, 成功 {success_count}, 失败 {error_count}")
    
    # 显示各提供商统计
    for provider in providers:
        stats = global_concurrency_controller.get_provider_stats(provider)
        logger.info(f"{provider}: {stats}")
    
    return {
        'total_time': total_time,
        'success_count': success_count,
        'error_count': error_count,
        'provider_stats': {p: global_concurrency_controller.get_provider_stats(p) for p in providers}
    }

async def test_cpu_intensive_operations():
    """测试CPU密集型操作性能"""
    logger.info("=== CPU密集型操作性能测试 ===")
    
    pool = await create_test_pool()
    performance_manager = PerformanceManager(pool)
    await performance_manager.start()
    
    def cpu_intensive_task(data_size: int):
        """模拟CPU密集型任务"""
        data = list(range(data_size))
        # 模拟复杂计算
        result = sum(x * x for x in data if x % 2 == 0)
        return result
    
    try:
        data_sizes = [10000, 50000, 100000]
        results = {}
        
        for size in data_sizes:
            # 测试同步执行
            start_time = time.time()
            sync_result = cpu_intensive_task(size)
            sync_time = time.time() - start_time
            
            # 测试异步执行
            start_time = time.time()
            async_result = await performance_manager.cpu_intensive_task(cpu_intensive_task, size)
            async_time = time.time() - start_time
            
            results[size] = {
                'sync_time': sync_time,
                'async_time': async_time,
                'sync_result': sync_result,
                'async_result': async_result,
                'improvement': (sync_time - async_time) / sync_time * 100 if sync_time > 0 else 0
            }
            
            logger.info(f"数据大小 {size}: 同步 {sync_time:.3f}s, 异步 {async_time:.3f}s, 改进 {results[size]['improvement']:.1f}%")
        
        return results
        
    finally:
        await performance_manager.stop()
        pool.close()
        await pool.wait_closed()

async def main():
    """主测试函数"""
    logger.info("开始性能测试")
    
    try:
        # 测试批量插入性能
        batch_results = await test_batch_insert_performance()
        
        # 测试并发控制性能
        concurrency_results = await test_concurrency_control()
        
        # 测试CPU密集型操作性能
        cpu_results = await test_cpu_intensive_operations()
        
        # 汇总结果
        logger.info("\n=== 性能测试总结 ===")
        logger.info("批量插入测试结果:")
        for batch_size, result in batch_results.items():
            logger.info(f"  批次 {batch_size}: 性能提升 {result['improvement']:.1f}%")
        
        logger.info(f"\n并发控制测试结果:")
        logger.info(f"  总耗时: {concurrency_results['total_time']:.2f}s")
        logger.info(f"  成功率: {concurrency_results['success_count']/(concurrency_results['success_count']+concurrency_results['error_count'])*100:.1f}%")
        
        logger.info(f"\nCPU密集型操作测试结果:")
        for size, result in cpu_results.items():
            logger.info(f"  数据大小 {size}: 性能提升 {result['improvement']:.1f}%")
        
    except Exception as e:
        logger.error(f"性能测试失败: {e}", exc_info=True)
    
    logger.info("性能测试完成")

if __name__ == "__main__":
    asyncio.run(main())
