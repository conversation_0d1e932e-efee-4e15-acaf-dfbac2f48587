# 这是一个示例环境配置文件。
# 请复制此文件为 .env，并根据您的实际需求修改其中的值。
# .env 文件通常不应提交到版本控制中。

# --- Docker 构建与运行配置 ---
# 设置运行容器的用户和组ID，以匹配您宿主机的用户，避免挂载卷的权限问题。
# 在Linux/macOS上，使用 `id -u` 和 `id -g` 命令获取。
PUID=1000
PGID=1000

# --- 数据库凭据 ---
# 强烈建议为生产环境生成随机且安全的密码。
MYSQL_DATABASE=danmaku_db
MYSQL_USER=danmaku_user
MYSQL_PASSWORD=change_me_to_a_strong_password
MYSQL_ROOT_PASSWORD=change_me_to_a_strong_root_password

# --- 应用安全配置 ---
# 使用 'openssl rand -base64 32' 命令生成一个强密钥。
JWT_SECRET_KEY=change_me_to_a_strong_jwt_secret

# --- 初始管理员配置 (可选) ---
ADMIN_USER=admin
#ADMIN_PASSWORD=a_pre-defined_password # 如果不设置，将生成随机密码