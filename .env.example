# LoveStory弹幕服务环境变量配置示例
# 复制此文件为 .env 并修改相应的值
# .env 文件不应提交到版本控制系统

# ==================== 服务器配置 ====================
# 服务监听地址和端口
LOVESTORY_SERVER__HOST=0.0.0.0
LOVESTORY_SERVER__PORT=7768

# ==================== 数据库配置 ====================
# 远程数据库连接信息（推荐用于生产环境）
LOVESTORY_DATABASE__HOST=your-remote-db-host.com
LOVESTORY_DATABASE__PORT=3306
LOVESTORY_DATABASE__USER=your_db_username
LOVESTORY_DATABASE__PASSWORD=your_db_password
LOVESTORY_DATABASE__NAME=your_db_name

# TLS/SSL安全连接配置
LOVESTORY_DATABASE__SSL_ENABLED=true
LOVESTORY_DATABASE__SSL_VERIFY_CERT=true
LOVESTORY_DATABASE__SSL_CA_PATH=config/ssl/ca-cert.pem
LOVESTORY_DATABASE__SSL_CERT_PATH=
LOVESTORY_DATABASE__SSL_KEY_PATH=

# ==================== JWT配置 ====================
# JWT密钥（请生成一个复杂的随机字符串）
# 使用命令生成: openssl rand -base64 32
LOVESTORY_JWT__SECRET_KEY=your_very_long_and_complex_secret_key_here
LOVESTORY_JWT__ALGORITHM=HS256
LOVESTORY_JWT__ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ==================== 管理员配置 ====================
# 初始管理员账户（可选）
LOVESTORY_ADMIN__INITIAL_USER=admin
LOVESTORY_ADMIN__INITIAL_PASSWORD=

# ==================== 第三方API配置 ====================
# Bangumi OAuth配置
LOVESTORY_BANGUMI__CLIENT_ID=bgm4222688b7532ef439
LOVESTORY_BANGUMI__CLIENT_SECRET=379c426b8f26b561642334445761361f

# 豆瓣Cookie（可选）
LOVESTORY_DOUBAN__COOKIE=

# ==================== 云数据库配置示例 ====================
#
# 阿里云RDS:
# LOVESTORY_DATABASE__HOST=rm-xxxxxxxx.mysql.rds.aliyuncs.com
# LOVESTORY_DATABASE__SSL_CA_PATH=config/ssl/rds-ca-2019.pem
#
# 腾讯云CDB:
# LOVESTORY_DATABASE__HOST=cdb-xxxxxxxx.tencentcdb.com
# LOVESTORY_DATABASE__SSL_CA_PATH=config/ssl/tencentdb-ca.pem
#
# AWS RDS:
# LOVESTORY_DATABASE__HOST=mydb.xxxxxxxx.us-east-1.rds.amazonaws.com
# LOVESTORY_DATABASE__SSL_CA_PATH=config/ssl/rds-ca-2019-root.pem
#
# 本地MySQL（不使用SSL）:
# LOVESTORY_DATABASE__HOST=127.0.0.1
# LOVESTORY_DATABASE__SSL_ENABLED=false

# ==================== 安全提醒 ====================
# 1. 务必修改JWT密钥为复杂随机字符串
# 2. 使用强密码保护数据库
# 3. 生产环境启用SSL证书验证
# 4. 定期更换密钥和密码