#!/usr/bin/env python3
"""
LoveStory弹幕服务本地启动脚本
自动检查环境、配置和依赖，然后启动服务
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                LoveStory弹幕服务 - 本地启动器                  ║
║                LoveStory Danmu Server - Local Launcher       ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("   需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """检查并安装依赖"""
    print("\n📦 检查项目依赖...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ 找不到requirements.txt文件")
        return False
    
    try:
        # 检查是否在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        if not in_venv:
            print("⚠️  建议在虚拟环境中运行")
            response = input("是否继续？(y/N): ").lower().strip()
            if response != 'y':
                print("请创建虚拟环境后再运行:")
                print("  python -m venv venv")
                if platform.system() == "Windows":
                    print("  venv\\Scripts\\activate")
                else:
                    print("  source venv/bin/activate")
                return False
        
        # 安装依赖
        print("正在安装/更新依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
        
        print("✅ 依赖检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def setup_config():
    """设置配置文件"""
    print("\n⚙️  检查配置文件...")
    
    config_dir = Path("config")
    config_file = config_dir / "config.yml"
    local_config_file = config_dir / "config.local.yml"
    
    if not config_file.exists():
        print("❌ 找不到config/config.yml文件")
        return False
    
    # 创建本地配置文件（如果不存在）
    if not local_config_file.exists():
        print("📝 创建本地配置文件...")
        
        local_config_content = """# 本地开发配置
# 服务器配置
server:
  host: "127.0.0.1"
  port: 7768

# 数据库配置 - 请根据实际情况修改
database:
  host: "127.0.0.1"
  port: 3306
  user: "danmuapi"
  password: "danmuapi"
  name: "danmuapi"

# JWT 配置
jwt:
  secret_key: "local-dev-secret-key-please-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 1440
"""
        
        try:
            with open(local_config_file, 'w', encoding='utf-8') as f:
                f.write(local_config_content)
            print(f"✅ 已创建本地配置文件: {local_config_file}")
            print("⚠️  请根据需要修改数据库配置")
        except Exception as e:
            print(f"❌ 创建本地配置文件失败: {e}")
            return False
    
    # 设置环境变量使用本地配置
    os.environ['CONFIG_FILE'] = str(local_config_file)
    print("✅ 配置文件检查完成")
    return True

def check_database():
    """检查数据库连接"""
    print("\n🗄️  检查数据库连接...")
    
    try:
        # 尝试导入数据库相关模块
        import aiomysql
        print("✅ 数据库驱动已安装")
        
        # 这里可以添加实际的数据库连接测试
        # 但为了简化，我们只检查驱动是否可用
        print("💡 提示: 请确保数据库服务正在运行")
        print("   - MySQL: 确保服务启动并创建了danmuapi数据库")
        print("   - 或使用配置文件中的TiDB Cloud连接")
        
        return True
        
    except ImportError as e:
        print(f"❌ 数据库驱动导入失败: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建必要目录...")
    
    directories = [
        "config/logs",
        "data",
        "temp"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")

def start_server():
    """启动服务器"""
    print("\n🚀 启动LoveStory弹幕服务...")
    print("=" * 60)
    
    try:
        # 使用uvicorn启动，支持热重载
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "src.main:app",
            "--host", "127.0.0.1",
            "--port", "7768",
            "--reload"
        ]
        
        print("启动命令:", " ".join(cmd))
        print("访问地址: http://127.0.0.1:7768")
        print("API文档: http://127.0.0.1:7768/docs")
        print("按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动服务
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("\n尝试使用备用方法启动:")
        print("python -m src.main")

def main():
    """主函数"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 设置配置
    if not setup_config():
        sys.exit(1)
    
    # 检查数据库
    if not check_database():
        print("⚠️  数据库检查失败，但仍可继续启动")
    
    # 创建目录
    create_directories()
    
    # 启动服务
    start_server()

if __name__ == "__main__":
    main()
