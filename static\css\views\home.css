#search-form {
    display: flex;
    gap: 10px;
}

#search-keyword {
    flex-grow: 1;
}

.advanced-search-controls {
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    min-height: 44px; /* 增加高度以改善布局 */
}

.advanced-search-controls label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

#episode-search-inputs {
    display: flex;
    align-items: center;
    gap: 5px;
}

#episode-search-inputs input {
    width: 80px;
}

#episode-search-inputs label {
    font-size: 14px;
    color: #606266;
}

.insert-help-text {
    font-size: 12px;
    color: #909399; /* A common grey color for help text */
    margin-left: 10px;
}

#log-output, #test-match-results {
    background-color: #282c34;
    color: #abb2bf;
    padding: 15px;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    font-family: 'Courier New', Courier, monospace;
}

#test-match-section form {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

#test-match-section input[type="text"] {
    flex-grow: 1;
}

.flex-grow-2 {
    flex-grow: 2;
}

/* Search Results */
.results-list-style {
    list-style-type: none;
    padding: 0;
    max-height: 400px;
    overflow-y: auto;
}

.results-list-style li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
}

.result-item-left {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-grow: 1;
    min-width: 0;
}

.results-list-style li .poster {
    width: 40px;
    height: 56px;
    object-fit: cover;
    border-radius: 4px;
    background-color: #eee;
    flex-shrink: 0;
}
.results-list-style li input[type="checkbox"] {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.results-list-style li .info {
    flex-grow: 1;
    overflow: hidden;
}

.results-list-style li .info .title {
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.results-list-style li .info .meta {
    font-size: 0.9em;
    color: #666;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.results-list-style li .info .extra-meta {
    font-size: 0.9em;
    color: var(--primary-color);
}

#results-filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 10px;
}

.filter-group-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.filter-buttons {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 5px 12px;
    border: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    color: #666;
    cursor: pointer;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
}

.filter-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.radio-group {
    display: flex;
    gap: 20px;
    align-items: center;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}
