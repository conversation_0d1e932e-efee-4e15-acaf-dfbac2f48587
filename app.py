#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoveStory弹幕服务 - 宝塔部署入口文件
适用于宝塔面板Python项目管理器部署
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入FastAPI应用
from src.main import app

# 宝塔面板需要的应用对象
application = app

if __name__ == "__main__":
    import uvicorn
    from src.config import settings
    
    # 开发环境直接运行
    uvicorn.run(
        app,
        host=settings.server.host,
        port=settings.server.port,
        reload=False
    )
