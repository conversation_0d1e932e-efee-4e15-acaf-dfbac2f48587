<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LoveStory弹幕服务</title>
    <!-- 新增：设置浏览器的标签页图标 -->
    <link rel="icon" href="/static/logo.png" type="image/png">
    <!-- Refactored CSS -->
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/components.css">
    <link rel="stylesheet" href="/static/css/views/auth.css">
    <link rel="stylesheet" href="/static/css/views/home.css">
    <link rel="stylesheet" href="/static/css/views/library.css">
    <link rel="stylesheet" href="/static/css/views/edit.css">
    <link rel="stylesheet" href="/static/css/views/tasks.css">
    <link rel="stylesheet" href="/static/css/views/tokens.css">
    <link rel="stylesheet" href="/static/css/views/sources.css">
    <link rel="stylesheet" href="/static/css/views/settings.css">
</head>
<body>
    <div id="app-container">
        <!-- 登录/注册视图 -->
        <div id="auth-view">
            <h1>LoveStory弹幕服务</h1>
            <div class="auth-wrapper">
                <!-- 登录表单 -->
                <form id="login-form">
                    <h2>登录</h2>
                    <input type="text" id="login-username" placeholder="用户名" required>
                    <input type="password" id="login-password" placeholder="密码" required>
                    <button type="submit">登录</button>
                </form>
            </div>
            <p id="auth-error" class="message error"></p>
        </div>

        <!-- 主应用视图 -->
        <div id="main-view" class="hidden">
            <header>
                <div class="header-title-group">
                    <img src="/static/logo.png" alt="Logo" id="header-logo">
                    <h1>LoveStory弹幕服务 v1.0.5</h1>
                </div>
                <div class="header-user-info">
                    <span id="current-user"></span>
                    <button id="logout-btn">退出登录</button>
                </div>
            </header>
            <div class="main-layout">
                <nav id="sidebar">
                    <ul>
                        <li><a href="#" class="nav-link active" data-view="home-view">主页</a></li>
                        <li><a href="#" class="nav-link" data-view="library-view">弹幕库</a></li>
                        <li><a href="#" class="nav-link" data-view="task-manager-view">任务管理器</a></li>
                        <li><a href="#" class="nav-link" data-view="token-manager-view">弹幕Token</a></li>
                        <li><a href="#" class="nav-link" data-view="sources-view">搜索源</a></li>
                        <li><a href="#" class="nav-link" data-view="settings-view">设置</a></li>
                    </ul>
                </nav>
                <main id="main-content">
                    <div id="home-view" class="content-view">
                        <div class="search-section">
                            <div class="section-header-with-action">
                                <h2>搜索</h2>
                                <button id="clear-cache-btn" title="清除所有搜索和分集列表的临时缓存">清除所有缓存</button>
                            </div>
                            <form id="search-form">
                                <input type="text" id="search-keyword" placeholder="输入番剧名称..." required>
                                <button type="submit">搜索</button>
                            </form>
                            <div class="advanced-search-controls">
                                <label>
                                    <input type="checkbox" id="enable-episode-search">
                                    电视节目精确搜索
                                </label>
                                <div id="episode-search-inputs" class="hidden">
                                    <label for="search-season">季度：</label>
                                    <input type="number" id="search-season" min="1">
                                    <label for="search-episode">集数：</label>
                                    <input type="number" id="search-episode" min="1">
                                    <button type="button" id="insert-episode-btn">插入</button>
                                    <span class="insert-help-text">需要填写季、集后可插入，其中季可单独插入</span>
                                </div>
                            </div>
                        </div>
                        <div id="results-section">
                            <h2>搜索结果</h2>
                            <div id="results-filter-controls" class="hidden">
                                <div class="filter-group-left">
                                    <button id="select-all-btn">全选</button>
                                    <div class="filter-buttons">
                                        <button id="filter-btn-movie" class="filter-btn active" data-type="movie">
                                            <span class="status-icon">✅</span> 电影/剧场版
                                        </button>
                                        <button id="filter-btn-tv_series" class="filter-btn active" data-type="tv_series">
                                            <span class="status-icon">✅</span> 电视节目
                                        </button>
                                    </div>
                                    <input type="text" id="results-filter-input" placeholder="在结果中过滤标题...">
                                </div>
                                <button id="bulk-import-btn">批量导入</button>
                            </div>
                            <div id="loader" class="hidden"></div>
                            <ul id="results-list" class="results-list-style"></ul>
                        </div>
                        <div id="logs-section">
                            <h2>日志/状态</h2>
                            <pre id="log-output"></pre>
                        </div>
                        <div id="test-match-section">
                            <h2>测试识别</h2>
                            <form id="test-match-form">
                                <input type="text" id="test-token-input" placeholder="输入弹幕Token..." required>
                                <input type="text" id="test-filename-input" class="flex-grow-2" placeholder="输入要测试匹配的文件名..." required>
                                <button type="submit">测试</button>
                            </form>
                            <pre id="test-match-results">测试结果将显示在这里。</pre>
                        </div>
                    </div>
                    <div id="bulk-import-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2>批量导入确认</h2>
                            <button id="cancel-bulk-import-btn">&lt; 返回主页</button>
                        </div>
                        <div class="form-card">
                            <p>检测到您选择的媒体标题不一致。请指定一个统一的名称用于导入，或从TMDB搜索。</p>
                            <h3>已选择的条目</h3>
                            <ul id="bulk-import-list" class="results-list-style">
                                <!-- Selected items will be populated here -->
                            </ul>
                        <div class="form-row" style="margin-top: 20px;">
                            <label>导入模式</label>
                            <div class="radio-group">
                                <label><input type="radio" name="bulk-import-mode" value="unified" checked> 统一导入为单个条目</label>
                                <label><input type="radio" name="bulk-import-mode" value="separate"> 作为多个独立条目导入</label>
                                </div>
                            </div>
                        <div id="unified-import-fields">
                            <div class="form-row" style="margin-top: 20px;">
                                <label for="final-import-name">最终导入名称</label>
                                <div class="input-with-icon">
                                    <input type="text" id="final-import-name" required>
                                    <button type="button" id="search-tmdb-for-bulk-btn" class="icon-btn" title="从TMDB搜索">🔍</button>
                                </div>
                            </div>
                            <div class="form-row">
                                <label for="final-import-tmdb-id">最终TMDB ID</label>
                                <input type="text" id="final-import-tmdb-id" placeholder="从TMDB搜索后自动填充" disabled>
                            </div>
                            </div>
                            <div class="form-actions">
                                <button id="confirm-bulk-import-btn">确认导入</button>
                            </div>
                        </div>
                    </div>
                    <div id="library-view" class="content-view hidden">
                        <div class="library-header">
                            <h2>弹幕库</h2>
                            <input type="text" id="library-search-input" placeholder="搜索已收录的影视...">
                        </div>
                        <table id="library-table">
                            <thead>
                                <tr>
                                    <th>海报</th>
                                    <th>影视名称</th>
                                    <th>类型</th>
                                    <th>季</th>
                                    <th>集数</th>
                                    <th>源数量</th>
                                    <th>收录时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="edit-anime-view" class="content-view hidden">
                        <div class="view-header-with-actions">
                            <h2>编辑影视信息</h2>
                            <div class="header-actions">
                                <button id="clear-all-cache-in-edit-btn" class="secondary-btn">清除所有缓存</button>
                            </div>
                        </div>
                        <div class="form-card">
                            <form id="edit-anime-form">
                                <input type="hidden" id="edit-anime-id">
                                <div class="form-row">
                                    <label for="edit-anime-title">影视名称</label>
                                    <input type="text" id="edit-anime-title" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-type">类型</label>
                                    <select id="edit-anime-type">
                                        <option value="tv_series">电视节目</option>
                                        <option value="movie">电影/剧场版</option>
                                    </select>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-season">季度</label>
                                    <input type="number" id="edit-anime-season" min="1" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-episode-count">集数</label>
                                    <input type="number" id="edit-anime-episode-count" min="1" placeholder="留空则自动计算">
                                </div>
                                
                                <div class="form-row">
                                    <label for="edit-anime-tmdbid">TMDB ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-tmdbid" placeholder="例如: 1396">
                                        <button type="button" id="search-tmdbid-btn" class="icon-btn" title="查找">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-egid">剧集组ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-egid" placeholder="TMDB Episode Group ID">
                                        <button type="button" id="select-egid-btn" class="icon-btn" title="查找/选择">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-bgmid">BGM ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-bgmid" placeholder="例如: 296100">
                                        <button type="button" id="search-bgmid-btn" class="icon-btn" title="查找">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-tvdbid">TVDB ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-tvdbid" placeholder="例如: 364093">
                                        <button type="button" id="search-tvdbid-btn" class="icon-btn" title="查找">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-doubanid">豆瓣ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-doubanid" placeholder="例如: 35297708">
                                        <button type="button" id="search-doubanid-btn" class="icon-btn" title="查找">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-imdbid">IMDB ID</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="edit-anime-imdbid" placeholder="例如: tt9140554">
                                        <button type="button" id="search-imdbid-btn" class="icon-btn" title="查找">🔍</button>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-name-en">英文名</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-name-en">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-name-jp">日文名</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-name-jp">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-name-romaji">罗马音</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-name-romaji">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-alias-cn-1">中文别名1</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-alias-cn-1">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-alias-cn-2">中文别名2</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-alias-cn-2">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <label for="edit-anime-alias-cn-3">中文别名3</label>
                                    <div class="input-with-apply-icon">
                                        <input type="text" id="edit-anime-alias-cn-3">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit">保存更改</button>
                                    <button type="button" id="back-to-library-from-edit-btn">返回</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div id="bangumi-search-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="bangumi-search-view-title">搜索 Bangumi ID</h2>
                            <button id="back-to-edit-anime-from-bgm-search-btn">&lt; 返回编辑</button>
                        </div>
                        <form id="bangumi-search-form" style="display: flex; gap: 10px; margin-bottom: 20px;">
                            <input type="text" id="bangumi-search-keyword" placeholder="输入番剧名称..." required style="flex-grow: 1;">
                            <button type="submit">搜索</button>
                        </form>
                        <ul id="bangumi-search-results-list" class="results-list-style">
                            <!-- Search results will be populated here -->
                        </ul>
                    </div>
                    <div id="douban-search-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="douban-search-view-title">搜索 豆瓣 ID</h2>
                            <button id="back-to-edit-anime-from-douban-search-btn">&lt; 返回编辑</button>
                        </div>
                        <form id="douban-search-form" style="display: flex; gap: 10px; margin-bottom: 20px;">
                            <input type="text" id="douban-search-keyword" placeholder="输入影视名称..." required style="flex-grow: 1;">
                            <button type="submit">搜索</button>
                        </form>
                        <ul id="douban-search-results-list" class="results-list-style"></ul>
                    </div>
                    <div id="tmdb-search-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="tmdb-search-view-title">搜索 TMDB ID</h2>
                            <button id="back-to-edit-anime-from-tmdb-search-btn">&lt; 返回编辑</button>
                        </div>
                        <form id="tmdb-search-form" style="display: flex; gap: 10px; margin-bottom: 20px;">
                            <input type="text" id="tmdb-search-keyword" placeholder="输入影视名称..." required style="flex-grow: 1;">
                            <button type="submit">搜索</button>
                        </form>
                        <ul id="tmdb-search-results-list" class="results-list-style">
                        </ul>
                    </div>
                    <div id="tvdb-search-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="tvdb-search-view-title">搜索 TVDB ID</h2>
                            <button id="back-to-edit-anime-from-tvdb-search-btn">&lt; 返回编辑</button>
                        </div>
                        <form id="tvdb-search-form" style="display: flex; gap: 10px; margin-bottom: 20px;">
                            <input type="text" id="tvdb-search-keyword" placeholder="输入影视名称..." required style="flex-grow: 1;">
                            <button type="submit">搜索</button>
                        </form>
                        <ul id="tvdb-search-results-list" class="results-list-style"></ul>
                    </div>
                    <div id="imdb-search-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="imdb-search-view-title">搜索 IMDb ID</h2>
                            <button id="back-to-edit-anime-from-imdb-search-btn">&lt; 返回编辑</button>
                        </div>
                        <form id="imdb-search-form" style="display: flex; gap: 10px; margin-bottom: 20px;">
                            <input type="text" id="imdb-search-keyword" placeholder="输入影视名称..." required style="flex-grow: 1;">
                            <button type="submit">搜索</button>
                        </form>
                        <ul id="imdb-search-results-list" class="results-list-style"></ul>
                    </div>
                    <div id="egid-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="egid-view-title">TMDB 剧集组</h2>
                            <button id="back-to-edit-from-egid-btn">&lt; 返回编辑</button>
                        </div>
                        <div id="egid-content-container">
                            <!-- Dynamic content (group list or episode list) will be rendered here -->
                        </div>
                    </div>
                    <div id="reassociate-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="reassociate-view-title">调整关联数据源</h2>
                            <button id="back-to-edit-from-reassociate-btn">&lt; 返回编辑</button>
                        </div>
                        <p id="reassociate-info-text"></p>
                        <div class="library-header">
                            <h3>选择目标作品</h3>
                            <input type="text" id="reassociate-search-input" placeholder="搜索目标作品...">
                        </div>
                        <table id="reassociate-target-table">
                            <thead>
                                <tr>
                                    <th>作品信息</th><th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="anime-detail-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <div class="anime-detail-header-main">
                                <img src="/static/placeholder.png" alt="Poster" id="detail-view-img" referrerpolicy="no-referrer">
                                <div>
                                    <h2 id="detail-view-title">作品标题</h2>
                                    <p id="detail-view-meta">元数据</p>
                                </div>
                            </div>
                            <button id="back-to-library-from-detail-btn"> &lt; 返回弹幕库</button>
                        </div>
                        <div class="section-header-with-action">
                            <h3>关联的数据源</h3>
                            <button id="reassociate-sources-from-detail-btn">调整关联数据源</button>
                        </div>
                        <div class="bulk-actions-bar">
                            <button id="delete-selected-sources-btn">删除选中</button>
                        </div>
                        <table id="source-detail-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="select-all-sources-checkbox" title="全选"></th>
                                    <th>源提供方</th><th>源媒体ID</th><th>状态</th><th>收录时间</th><th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="source-detail-table-body"></tbody>
                        </table>
                    </div>
                    <div id="episode-list-view" class="content-view hidden">
                        <!-- 分集列表将在这里动态生成 -->
                    </div>
                    <div id="edit-episode-view" class="content-view hidden">
                        <div class="form-card">
                            <h2>编辑分集信息</h2>
                            <form id="edit-episode-form">
                                <input type="hidden" id="edit-episode-id">
                                <input type="hidden" id="edit-episode-source-id">
                                <input type="hidden" id="edit-episode-anime-title">
                                <input type="hidden" id="edit-episode-anime-id">
                                <div class="form-row">
                                    <label for="edit-episode-title">分集标题</label>
                                    <input type="text" id="edit-episode-title" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-episode-index">集数</label>
                                    <input type="number" id="edit-episode-index" min="1" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-episode-url">官方链接</label>
                                    <input type="text" id="edit-episode-url">
                                </div>
                                <div class="form-actions">
                                    <button type="submit">保存更改</button>
                                    <button type="button" id="back-to-episodes-from-edit-btn">返回</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div id="danmaku-list-view" class="content-view hidden">
                        <!-- 弹幕列表将在这里动态生成 -->
                    </div>
                    <div id="task-manager-view" class="content-view hidden">
                        <div class="settings-sub-nav">
                            <button class="sub-nav-btn active" data-subview="running-tasks-subview">运行中任务</button>
                            <button class="sub-nav-btn" data-subview="scheduled-tasks-subview">定时任务</button>
                        </div>

                        <div id="running-tasks-subview" class="settings-subview">
                            <div class="library-header">
                                <h2>运行中任务</h2>
                                <div class="view-controls">
                                    <div class="task-actions">
                                        <button id="pause-running-task-btn" class="control-btn" title="暂停/恢复功能暂未实现" disabled>暂停/恢复</button>
                                        <button id="delete-running-task-btn" class="control-btn danger">删除任务</button>
                                    </div>
                                    <input type="text" id="running-tasks-search-input" placeholder="按任务标题搜索...">
                                    <div id="running-tasks-filter-buttons" class="filter-buttons">
                                        <button class="filter-btn" data-status-filter="all">全部</button>
                                        <button class="filter-btn" data-status-filter="completed">已完成</button>
                                        <button class="filter-btn active" data-status-filter="in_progress">进行中</button>
                                    </div>
                                </div>
                            </div>
                            <ul id="task-list"></ul>
                        </div>

                        <div id="scheduled-tasks-subview" class="settings-subview hidden">
                            <div class="library-header">
                                <h2>定时任务</h2>
                                <button id="add-scheduled-task-btn">添加定时任务</button>
                            </div>
                            <p>定时任务用于自动执行维护操作，例如自动更新和映射TMDB数据。使用标准的Cron表达式格式。</p>
                            <table id="scheduled-tasks-table">
                                <thead>
                                    <tr><th>名称</th><th>类型</th><th>Cron表达式</th><th>状态</th><th>上次运行</th><th>下次运行</th><th>操作</th></tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div id="edit-scheduled-task-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="edit-scheduled-task-title">添加定时任务</h2>
                            <button id="back-to-tasks-from-edit-btn">&lt; 返回任务列表</button>
                        </div>
                        <div class="form-card">
                            <form id="edit-scheduled-task-form">
                                <input type="hidden" id="edit-scheduled-task-id">
                                <div class="form-row">
                                    <label for="edit-scheduled-task-name">任务名称</label>
                                    <input type="text" id="edit-scheduled-task-name" placeholder="例如：每日TMDB更新" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-scheduled-task-type">任务类型</label>
                                    <select id="edit-scheduled-task-type" required></select>
                                </div>
                                <div class="form-row">
                                    <label for="edit-scheduled-task-cron">Cron表达式</label>
                                    <input type="text" id="edit-scheduled-task-cron" placeholder="例如：0 2 * * * (每天凌晨2点)" required>
                                </div>
                                <div class="form-row">
                                    <label for="edit-scheduled-task-enabled">是否启用</label>
                                    <input type="checkbox" id="edit-scheduled-task-enabled" checked>
                                </div>
                                <div class="form-actions">
                                    <button type="submit">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div id="token-manager-view" class="content-view hidden">
                        <div class="library-header">
                            <h2>弹幕Token管理</h2>
                            <button id="add-token-btn">添加Token</button>
                        </div>
                        <p>本项目参考了<a href="https://api.dandanplay.net/swagger/index.html" target="_blank">dandanplayapi</a>，同时增加了使用访问令牌管理弹幕api,支持<a href="https://t.me/yamby_release" target="_blank">yamby</a>、<a href="https://play.google.com/store/search?q=hills&c=apps" target="_blank">hills</a>、<a href="https://apps.microsoft.com/detail/9NB0H051M4V4" target="_blank">小幻影视</a>。</p>
                        <div class="domain-config-section">
                            <h3>自定义域名设置</h3>
                            <p>设置后，复制按钮将自动拼接 "http(s)://域名(ip):端口(port)/api/Token值" 格式的完整URL。</p>
                            <div class="form-row">
                                <label for="custom-domain-input">自定义域名</label>
                                <input type="text" id="custom-domain-input" placeholder="例如: https://danmu.example.com">
                                <button id="save-domain-btn">保存域名</button>
                            </div>
                            <p id="domain-save-message" class="message"></p>
                        </div>
                        <div class="ua-filter-section">
                            <h3>全局 User-Agent 过滤</h3>
                            <p>对所有通过Token的访问请求进行UA过滤。模式为 "off" 时不过滤。</p>
                            <div class="form-row">
                                <label for="ua-filter-mode">过滤模式</label>
                                <select id="ua-filter-mode">
                                    <option value="off">关闭 (Off)</option>
                                    <option value="blacklist">黑名单 (Blacklist)</option>
                                    <option value="whitelist">白名单 (Whitelist)</option>
                                </select>
                                <button id="save-ua-mode-btn">保存模式</button>
                                <button id="manage-ua-list-btn">管理名单</button>
                            </div>
                            <p id="ua-mode-save-message" class="message"></p>
                        </div>
                        <table id="token-table">
                            <thead>
                                <tr>
                                    <th>名称</th><th>Token</th><th>状态</th><th>创建时间</th><th>有效期</th><th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div id="add-token-view" class="content-view hidden">
                        <h2>添加新Token</h2>
                        <form id="add-token-form">
                            <p>为新的API Token指定一个描述性名称，Token值将自动生成。</p>
                            <div class="form-row">
                                <label for="add-token-name">名称</label>
                                <input type="text" id="add-token-name" placeholder="例如：我的dandanplay客户端" required>
                            </div>
                            <div class="form-row">
                                <label for="add-token-validity">有效期</label>
                                <select id="add-token-validity">
                                    <option value="permanent" selected>永久</option>
                                    <option value="1d">1 天</option>
                                    <option value="7d">7 天</option>
                                    <option value="30d">30 天</option>
                                    <option value="180d">6 个月</option>
                                    <option value="365d">1 年</option>
                                </select>
                            </div>
                            <div class="form-actions">
                                <button type="submit">保存</button>
                                <button type="button" id="back-to-tokens-from-add-btn">返回</button>
                            </div>
                        </form>
                    </div>
                    <div id="sources-view" class="content-view hidden">
                        <div class="settings-sub-nav">
                            <button class="sub-nav-btn active" data-subview="danmaku-sources-subview">弹幕搜索源</button>
                            <button class="sub-nav-btn" data-subview="metadata-sources-subview">元信息搜索源</button>
                        </div>

                        <div id="danmaku-sources-subview" class="settings-subview">
                            <div class="source-controls">
                                <h2>弹幕搜索源</h2>
                                <div class="source-actions">
                                    <button id="save-danmaku-sources-btn">保存设置</button>
                                    <button id="toggle-danmaku-source-btn">启用/禁用</button>
                                    <button id="move-danmaku-source-up-btn">上移</button>
                                    <button id="move-danmaku-source-down-btn">下移</button>
                                </div>
                            </div>
                            <ul id="danmaku-sources-list"></ul>
                        </div>

                        <div id="metadata-sources-subview" class="settings-subview hidden">
                            <div class="source-controls">
                                <h2>元信息搜索源</h2>
                                <div class="source-actions">
                                    <button id="save-metadata-sources-btn">保存排序</button>
                                    <button id="move-metadata-source-up-btn">上移</button>
                                    <button id="move-metadata-source-down-btn">下移</button>
                                </div>
                            </div>
                            <ul id="metadata-sources-list"></ul>
                        </div>
                    </div>
                    <div id="ua-settings-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2>管理UA名单</h2>
                            <button id="back-to-tokens-from-ua-btn">&lt; 返回Token管理</button>
                        </div>
                        <div class="form-card">
                            <form id="add-ua-rule-form">
                                <div class="form-row">
                                    <label for="add-ua-string">添加UA字符串</label>
                                    <input type="text" id="add-ua-string" placeholder="输入要匹配的UA关键字" required>
                                    <button type="submit">添加</button>
                                </div>
                            </form>
                            <table id="ua-rules-table">
                                <thead><tr><th>UA字符串</th><th>创建时间</th><th>操作</th></tr></thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                    <div id="token-log-view" class="content-view hidden">
                        <div class="view-header-flexible">
                            <h2 id="token-log-view-title">Token访问日志</h2>
                            <button id="back-to-tokens-from-log-btn">&lt; 返回Token管理</button>
                        </div>
                        <table id="token-log-table">
                            <thead><tr><th>访问时间</th><th>IP地址</th><th>状态</th><th>路径</th><th>User-Agent</th></tr></thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div id="settings-view" class="content-view hidden">
                        <h2>设置</h2>
                        <div class="settings-sub-nav">
                            <button class="sub-nav-btn active" data-subview="account-settings-subview">账户安全</button>
                            <button class="sub-nav-btn" data-subview="webhook-settings-subview">Webhook</button>
                            <button class="sub-nav-btn" data-subview="bangumi-settings-subview">Bangumi配置</button>
                            <button class="sub-nav-btn" data-subview="tmdb-settings-subview">TMDB配置</button>
                            <button class="sub-nav-btn" data-subview="douban-settings-subview">豆瓣配置</button>
                            <button class="sub-nav-btn" data-subview="tvdb-settings-subview">TVDB配置</button>
                        </div>

                        <div id="account-settings-subview" class="settings-subview">
                            <div class="form-card">
                                <h3>修改密码</h3>
                                <form id="change-password-form">
                                    <p>如果您是使用初始随机密码登录的，建议您在此修改为自己的密码。</p>
                                    <div class="form-row">
                                        <label for="old-password">当前密码</label>
                                        <input type="password" id="old-password" required>
                                    </div>
                                    <div class="form-row">
                                        <label for="new-password">新密码</label>
                                        <input type="password" id="new-password" placeholder="至少8位" required>
                                    </div>
                                    <div class="form-row">
                                        <label for="confirm-password">确认新密码</label>
                                        <input type="password" id="confirm-password" required>
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit">确认修改</button>
                                    </div>
                                    <p id="password-change-message" class="message"></p>
                                </form>
                            </div>
                        </div>

                        <div id="webhook-settings-subview" class="settings-subview hidden">
                            <div class="form-card">
                                <h3>Webhook 配置</h3>
                                <p>Webhook 用于接收来自外部服务的通知，以实现自动化导入。请将下方对应服务的 URL 填入其 Webhook 通知设置中。</p>
                                <p>URL 格式为：<code>http(s)://域名(ip):端口(port)/api/webhook/{服务名}?api_key={你的API Key}</code></p>
                                <div class="form-row">
                                    <label for="webhook-api-key">API Key</label>
                                    <div class="input-with-icon">
                                        <input type="text" id="webhook-api-key" readonly>
                                        <button type="button" id="regenerate-webhook-key-btn" class="icon-btn" title="重新生成">🔄</button>
                                    </div>
                                </div>
                                <div id="webhook-handlers-list-container">
                                    <h4>已加载的服务:</h4>
                                    <ul id="webhook-handlers-list"></ul>
                                </div>
                            </div>
                        </div>

                        <div id="bangumi-settings-subview" class="settings-subview hidden">
                            <div class="form-card">
                                <h3>Bangumi 授权</h3>
                                <div id="bangumi-auth-state-unauthenticated">
                                    <p>当前未授权。授权后可使用更多功能。</p>
                                </div>
                                <div id="bangumi-auth-state-authenticated" class="hidden">
                                    <p>状态: 已作为 <strong id="bangumi-user-nickname"></strong> 授权</p>
                                    <p>用户ID: <span id="bangumi-user-id"></span></p>
                                    <p>授权时间: <span id="bangumi-authorized-at"></span></p>
                                    <p>过期时间: <span id="bangumi-expires-at"></span></p>
                                    <img id="bangumi-user-avatar" src="/static/placeholder.png" alt="avatar" style="width: 40px; height: 40px; border-radius: 50%; margin-top: 10px;">
                                </div>
                                <div id="bangumi-auth-actions">
                                    <div class="form-actions">
                                        <button id="bangumi-login-btn">通过 Bangumi 登录</button>
                                        <button id="bangumi-logout-btn" class="hidden" style="background-color: var(--error-color);">注销</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="tmdb-settings-subview" class="settings-subview hidden">
                            <div class="form-card">
                                <h3>TMDB API 配置</h3>
                                <p>请从 <a href="https://www.themoviedb.org/settings/api" target="_blank">TMDB官网</a> 获取您的 API Key (v3 auth)。</p>
                                <form id="tmdb-settings-form">
                                    <div class="form-row">
                                        <label for="tmdb-api-key">API Key (v3)</label>
                                        <input type="password" id="tmdb-api-key" placeholder="在此输入您的 TMDB API Key">
                                    </div>
                                    <div class="form-row">
                                        <label for="tmdb-api-base-url">API 域名</label>
                                        <input type="text" id="tmdb-api-base-url" placeholder="例如: https://api.themoviedb.org">
                                    </div>
                                    <div class="form-row">
                                        <label for="tmdb-image-base-url">图片域名</label>
                                        <input type="text" id="tmdb-image-base-url" placeholder="例如: https://image.tmdb.org/t/p/w500">
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit">保存</button>
                                    </div>
                                </form>
                                <p id="tmdb-save-message" class="message"></p>
                            </div>
                        </div>

                        <div id="douban-settings-subview" class="settings-subview hidden">
                            <div class="form-card">
                                <h3>豆瓣 Cookie 配置</h3>
                                <p>豆瓣搜索通常无需配置即可使用。如果遇到搜索失败或403错误，可以尝试在此处配置您的豆瓣账户Cookie以提高请求成功率。请从浏览器开发者工具中获取。</p>
                                <form id="douban-settings-form">
                                    <div class="form-row">
                                        <label for="douban-cookie">Cookie</label>
                                        <textarea id="douban-cookie" rows="4" placeholder="选填。如果搜索失败或被限制，请在此输入Cookie"></textarea>
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit">保存</button>
                                    </div>
                                </form>
                                <p id="douban-save-message" class="message"></p>
                            </div>
                        </div>

                        <div id="tvdb-settings-subview" class="settings-subview hidden">
                            <div class="form-card">
                                <h3>TVDB API 配置</h3>
                                <p>此项目需要 TheTVDB V4 API Key。您可以从 <a href="https://thetvdb.com/subscribe" target="_blank">TheTVDB官网</a> 获取您自己的 Key。</p>
                                <form id="tvdb-settings-form">
                                    <div class="form-row">
                                        <label for="tvdb-api-key">API Key</label>
                                        <input type="password" id="tvdb-api-key" placeholder="在此输入您的 TVDB API Key">
                                    </div>
                                    <div class="form-actions">
                                        <button type="submit">保存</button>
                                    </div>
                                </form>
                                <p id="tvdb-save-message" class="message"></p>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <div id="generic-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Modal Title</h3>
                <button id="modal-close-btn" class="modal-close">&times;</button>
            </div>
            <div id="modal-body" class="modal-body">
                <!-- Dynamic content goes here -->
            </div>
            <div class="modal-footer">
                <button id="modal-cancel-btn" class="secondary-btn">取消</button>
                <button id="modal-save-btn">保存</button>
            </div>
        </div>
    </div>
    <script type="module" src="/static/js/main.js"></script>
</body>
</html>

