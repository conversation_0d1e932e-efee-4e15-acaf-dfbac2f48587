#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LoveStory弹幕服务快速配置脚本
一键配置远程数据库和TLS连接
"""

import os
import sys
import yaml
import secrets
import string
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("🚀 LoveStory弹幕服务 - 快速配置向导")
    print("=" * 70)
    print("本向导将帮助您快速配置远程数据库和TLS安全连接")
    print()

def get_database_info():
    """获取数据库连接信息"""
    print("📋 请输入数据库连接信息:")
    print("-" * 40)
    
    db_info = {}
    db_info['host'] = input("数据库地址: ").strip()
    db_info['port'] = int(input("数据库端口 [3306]: ").strip() or "3306")
    db_info['user'] = input("用户名: ").strip()
    db_info['password'] = input("密码: ").strip()
    db_info['name'] = input("数据库名: ").strip()
    
    return db_info

def get_ssl_config():
    """获取SSL配置"""
    print("\n🔐 SSL/TLS安全连接配置:")
    print("-" * 40)
    
    ssl_enabled = input("是否启用SSL连接? (Y/n): ").strip().lower()
    if ssl_enabled in ['n', 'no']:
        return {
            'ssl_enabled': False,
            'ssl_verify_cert': False,
            'ssl_ca_path': None,
            'ssl_cert_path': None,
            'ssl_key_path': None
        }
    
    ssl_verify = input("是否验证服务器证书? (Y/n): ").strip().lower()
    ssl_verify_cert = ssl_verify not in ['n', 'no']
    
    ssl_ca_path = None
    if ssl_verify_cert:
        ca_path = input("CA证书文件路径 [config/ssl/ca-cert.pem]: ").strip()
        ssl_ca_path = ca_path or "config/ssl/ca-cert.pem"
    
    return {
        'ssl_enabled': True,
        'ssl_verify_cert': ssl_verify_cert,
        'ssl_ca_path': ssl_ca_path,
        'ssl_cert_path': None,
        'ssl_key_path': None
    }

def generate_jwt_secret():
    """生成JWT密钥"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(64))

def create_config(db_info, ssl_config):
    """创建配置文件"""
    config = {
        'server': {
            'host': '0.0.0.0',
            'port': 7768
        },
        'database': {
            **db_info,
            **ssl_config
        },
        'jwt': {
            'secret_key': generate_jwt_secret(),
            'algorithm': 'HS256',
            'access_token_expire_minutes': 1440
        }
    }
    
    return config

def save_config(config):
    """保存配置文件"""
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "config.yml"
    
    # 备份现有配置
    if config_file.exists():
        backup_file = config_dir / "config.yml.backup"
        config_file.rename(backup_file)
        print(f"✅ 已备份原配置文件到: {backup_file}")
    
    # 保存新配置
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
    
    print(f"✅ 配置文件已保存: {config_file}")
    return config_file

def create_ssl_directory():
    """创建SSL证书目录"""
    ssl_dir = Path("config/ssl")
    ssl_dir.mkdir(parents=True, exist_ok=True)
    
    readme_file = ssl_dir / "README.md"
    if not readme_file.exists():
        readme_content = """# SSL证书目录

请将您的数据库SSL证书文件放置在此目录下：

- `ca-cert.pem` - CA根证书文件
- `client-cert.pem` - 客户端证书文件（可选）
- `client-key.pem` - 客户端私钥文件（可选）

## 常见云服务商证书下载地址：

- **阿里云RDS**: 控制台 → 数据安全性 → SSL
- **腾讯云CDB**: 控制台 → 数据安全 → SSL加密
- **AWS RDS**: 使用AWS提供的根证书
- **Azure**: 下载BaltimoreCyberTrustRoot证书
"""
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    print(f"✅ SSL证书目录已创建: {ssl_dir}")
    return ssl_dir

def test_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    test_script = Path("test_db_connection.py")
    if not test_script.exists():
        print("❌ 测试脚本不存在，跳过连接测试")
        return
    
    choice = input("是否现在测试数据库连接? (Y/n): ").strip().lower()
    if choice in ['n', 'no']:
        print("⏭️  跳过连接测试")
        return
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, str(test_script)], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据库连接测试通过")
        else:
            print("❌ 数据库连接测试失败")
            print("错误信息:", result.stderr)
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")

def show_next_steps(config_file, ssl_dir, ssl_enabled):
    """显示后续步骤"""
    print("\n" + "=" * 70)
    print("🎉 配置完成！")
    print("=" * 70)
    
    print("📝 下一步操作:")
    print(f"1. 检查配置文件: {config_file}")
    
    if ssl_enabled:
        print(f"2. 上传SSL证书到: {ssl_dir}")
        print("   - 下载您的数据库CA证书")
        print("   - 重命名为 ca-cert.pem")
        print(f"   - 放置到 {ssl_dir} 目录")
        print("3. 测试连接: python test_db_connection.py")
        print("4. 启动服务:")
    else:
        print("2. 测试连接: python test_db_connection.py")
        print("3. 启动服务:")
    
    print("   - 宝塔部署: 在Python项目管理器中启动")
    print("   - 手动启动: python start.py")
    print("   - 或者: python app.py")
    
    print("\n💡 提示:")
    print("- 首次启动会自动创建数据库表")
    print("- 默认管理员用户: admin")
    print("- 初始密码会在启动日志中显示")
    print("=" * 70)

def main():
    """主函数"""
    try:
        print_banner()
        
        # 1. 获取数据库信息
        db_info = get_database_info()
        
        # 2. 获取SSL配置
        ssl_config = get_ssl_config()
        
        # 3. 创建配置
        config = create_config(db_info, ssl_config)
        
        # 4. 保存配置
        config_file = save_config(config)
        
        # 5. 创建SSL目录
        ssl_dir = create_ssl_directory()
        
        # 6. 测试连接
        if not ssl_config['ssl_enabled'] or (ssl_config['ssl_ca_path'] and Path(ssl_config['ssl_ca_path']).exists()):
            test_connection()
        
        # 7. 显示后续步骤
        show_next_steps(config_file, ssl_dir, ssl_config['ssl_enabled'])
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 配置过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
