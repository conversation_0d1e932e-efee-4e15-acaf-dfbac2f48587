# LoveStory弹幕服务 - 宝塔面板部署指南

## 📋 部署前准备

### 1. 服务器环境要求
- Python 3.8+ (推荐 3.11)
- MySQL 5.7+ 或 MySQL 8.0+
- 宝塔面板 7.0+

### 2. 安装Python环境
在宝塔面板中：
1. 进入 **软件商店** → **运行环境**
2. 安装 **Python项目管理器**
3. 安装 **Python 3.11** (推荐版本)

### 3. 安装MySQL数据库
在宝塔面板中：
1. 进入 **软件商店** → **运行环境**
2. 安装 **MySQL 8.0**
3. 创建数据库和用户

## 🗄️ 数据库配置

### 1. 创建数据库
```sql
-- 创建数据库
CREATE DATABASE danmu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER 'danmu_user'@'localhost' IDENTIFIED BY 'your_strong_password';
GRANT ALL PRIVILEGES ON danmu_db.* TO 'danmu_user'@'localhost';
FLUSH PRIVILEGES;
```

### 2. 修改配置文件
编辑 `config/config.yml` 文件：
```yaml
database:
  host: "127.0.0.1"
  port: 3306
  user: "danmu_user"          # 修改为您的数据库用户名
  password: "your_password"   # 修改为您的数据库密码
  name: "danmu_db"           # 修改为您的数据库名称
```

## 🚀 宝塔部署步骤

### 1. 上传项目文件
1. 将项目文件上传到服务器目录（如：`/www/wwwroot/danmu`）
2. 确保文件权限正确：`chmod -R 755 /www/wwwroot/danmu`

### 2. 创建Python项目
1. 进入宝塔面板 → **Python项目管理器**
2. 点击 **添加项目**
3. 填写项目信息：
   - **项目名称**: LoveStory弹幕服务
   - **项目路径**: `/www/wwwroot/danmu`
   - **启动文件**: `app.py`
   - **Python版本**: 选择已安装的Python 3.11
   - **端口**: 7768 (或其他可用端口)

### 3. 安装依赖包
在项目管理页面：
1. 点击项目名称进入详情
2. 在 **模块** 标签页中，点击 **安装模块**
3. 选择 **从文件安装**，选择 `requirements.txt`
4. 等待安装完成

### 4. 配置项目
1. 修改 `config/config.yml` 中的数据库配置
2. 修改JWT密钥：
   ```yaml
   jwt:
     secret_key: "your_unique_secret_key_here"  # 请生成一个复杂的密钥
   ```

### 5. 启动项目
1. 在Python项目管理器中点击 **启动**
2. 查看日志确认启动成功
3. 访问 `http://您的服务器IP:7768` 测试

## 🌐 反向代理配置（可选）

### 1. 创建网站
1. 在宝塔面板中创建一个新网站
2. 域名填写您的域名（如：`danmu.yourdomain.com`）

### 2. 配置反向代理
在网站设置中：
1. 点击 **反向代理**
2. 添加反向代理：
   - **代理名称**: danmu
   - **目标URL**: `http://127.0.0.1:7768`
   - **发送域名**: `$host`

### 3. 配置SSL证书（推荐）
1. 在网站设置中申请Let's Encrypt免费证书
2. 开启强制HTTPS

## 🔧 常见问题解决

### 1. 端口被占用
- 修改 `config/config.yml` 中的端口号
- 在宝塔面板中修改项目端口
- 重启项目

### 2. 数据库连接失败
- 检查数据库服务是否启动
- 验证数据库用户名和密码
- 确认数据库权限设置

### 3. 模块安装失败
- 检查Python版本是否正确
- 手动安装失败的模块：
  ```bash
  pip install 模块名
  ```

### 4. 权限问题
- 设置正确的文件权限：
  ```bash
  chown -R www:www /www/wwwroot/danmu
  chmod -R 755 /www/wwwroot/danmu
  ```

## 📝 初始使用

### 1. 首次登录
- 访问Web界面：`http://您的域名或IP:端口`
- 默认用户名：`admin`
- 初始密码：查看项目日志获取随机生成的密码

### 2. 修改密码
- 登录后立即在 **设置** → **账户安全** 中修改密码

### 3. 配置API密钥
- 在 **搜索源** 页面配置各平台的API密钥
- 在 **设置** 页面配置其他选项

## 🔄 项目管理

### 启动项目
```bash
# 在项目目录下
python app.py
```

### 停止项目
在宝塔Python项目管理器中点击 **停止**

### 重启项目
在宝塔Python项目管理器中点击 **重启**

### 查看日志
在项目详情页面的 **日志** 标签页查看运行日志

## 📞 技术支持

如遇到部署问题，请检查：
1. Python版本是否正确
2. 数据库配置是否正确
3. 依赖包是否完整安装
4. 文件权限是否正确
5. 端口是否被占用

---

**注意**: 部署完成后，请及时修改默认密码和JWT密钥，确保系统安全。
