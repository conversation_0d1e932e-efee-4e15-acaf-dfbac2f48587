/* Generic Components */
.content-view > div:not(.form-card):not(.view-header-flexible):not(.view-header-with-actions) {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.library-header, .section-header-with-action {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.library-header h2, .section-header-with-action h2 {
    border: none;
    padding: 0;
    margin: 0;
}

.view-header-flexible {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
}

.view-header-with-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
}

.view-header-with-actions h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.form-card {
    max-width: 700px;
    margin: 20px auto;
    padding: 20px;
    background: var(--card-bg);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

.form-card h2, .form-card h3 {
    border-bottom: none;
    padding-bottom: 0;
    margin: 0 0 10px 0;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 15px;
}

.form-row label {
    width: 120px;
    text-align: right;
    flex-shrink: 0;
    font-weight: 500;
    color: #606266;
}

.form-row input, .form-row select {
    flex-grow: 1;
}

.form-row input[type="checkbox"] {
    width: auto;
    flex-grow: 0;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

input[type="text"],
input[type="password"],
input[type="number"],
select {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 16px;
}

input:disabled {
    background-color: #f5f7fa;
    color: #c0c4cc;
    cursor: not-allowed;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    background-color: var(--primary-color);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #357abd;
}

button:disabled {
    background-color: #a0c7e4;
    cursor: not-allowed;
}

#logout-btn {
    background-color: var(--error-color);
    padding: 8px 12px;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.message {
    text-align: center;
    margin-top: 10px;
    font-size: 0.9em;
    min-height: 1.2em;
}

.message.success { color: var(--success-color); }
.message.error { color: var(--error-color); }

#loader {
    border: 4px solid #f3f3f3;
    border-radius: 50%;
    border-top: 4px solid var(--primary-color);
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 12px 20px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

th {
    background-color: var(--secondary-color);
    font-weight: 600;
}

.actions-cell {
    /* 移除右对齐，让内容自然左对齐 */
}

.actions-cell .action-buttons-wrapper {
    display: flex; /* 改为 flex 以便更好地控制内部元素 */
    align-items: center;
    gap: 8px;
}

.secondary-btn {
    background-color: #6c757d;
}

.secondary-btn:hover {
    background-color: #5a6268;
}

/* --- Modal Styles --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: var(--bg-color);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    width: 90%;
    max-width: 600px;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    border: none;
    padding: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
}

.modal-body .form-row {
    margin-bottom: 15px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
}

.modal-help-text {
    font-size: 0.9em;
    color: #606266;
    margin-bottom: 15px;
    background-color: var(--secondary-color);
    padding: 10px;
    border-radius: 4px;
    line-height: 1.5;
}

.modal-help-text a {
    color: var(--primary-color);
    text-decoration: none;
}

.modal-help-text a:hover {
    text-decoration: underline;
}
