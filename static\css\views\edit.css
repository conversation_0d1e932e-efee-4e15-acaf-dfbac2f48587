#edit-anime-form input::placeholder {
    text-align: left;
    color: #aaa;
}

.input-with-icon {
    display: flex;
    align-items: stretch;
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    overflow: hidden;
}

.input-with-icon.disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.input-with-icon input {
    flex-grow: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 10px;
    font-size: 16px;
}

.input-with-icon .icon-btn {
    flex-shrink: 0;
    width: 38px;
    border: none;
    border-left: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    transition: background-color 0.2s;
}

.input-with-icon .icon-btn:hover {
    background-color: #e9ecef;
}

.input-with-icon .icon-btn:disabled {
    background-color: #f5f7fa;
    cursor: not-allowed;
}

.input-with-icon .icon-btn:disabled:hover {
    background-color: #f5f7fa;
}

.input-with-apply-icon {
    display: flex;
    align-items: stretch;
    flex-grow: 1;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-color);
    overflow: hidden;
}

.input-with-apply-icon input {
    flex-grow: 1;
    border: none;
    outline: none;
    background: transparent;
    padding: 10px;
    font-size: 16px;
}

.input-with-apply-icon .apply-btn {
    flex-shrink: 0;
    width: 38px;
    border: none;
    border-left: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    padding: 0;
    transition: background-color 0.2s;
    color: var(--primary-color);
}

/* Styles for the TMDB Episode Group list to prevent layout issues with long descriptions */
.egid-group-list li {
    /* 修正：将对齐方式改回居中，以确保按钮在垂直方向上始终对齐 */
    align-items: center;
}

.egid-group-list .info {
    /* Allow the info block to shrink and not push the buttons */
    min-width: 0;
}

.egid-group-list .meta {
    /* Allow the description to wrap */
    white-space: normal;
    /* To prevent very long words from breaking the layout */
    word-break: break-word; 
    /* Remove ellipsis since it's now multi-line */
    overflow: visible;
    text-overflow: clip;
}

.egid-group-list .actions {
    /* Ensure buttons don't shrink */
    flex-shrink: 0;
    margin-left: 15px; /* Add some space */
}

/* --- Styles for Metadata Search Results (Bangumi, TMDB, Douban) --- */

.metadata-search-list li {
    /* 固定高度 (海报56px + 上下padding 2*10px) */
    height: 78px;
    box-sizing: border-box;
}

.metadata-search-list li .info {
    /* 让 info 块和海报一样高，并使用 flex 布局 */
    height: 56px;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 核心：将内容推向两端 */
}

.metadata-search-list li .info .title-container {
    display: flex;
    align-items: baseline; /* 基线对齐，看起来更专业 */
}

.metadata-search-list li .info .title-container .title {
    margin: 0;
    padding: 0;
    border: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
}

.id-tag {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.9em;
    margin-right: 10px;
    flex-shrink: 0;
}

.metadata-search-list li .info .meta {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.9em;
    color: #666;
    margin: 0;
}

.metadata-search-list li .select-btn {
    flex-shrink: 0;
}
