-- LoveStory弹幕服务数据库初始化脚本
-- 适用于宝塔面板MySQL部署

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS danmu_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE danmu_db;

-- 创建用户（如果不存在）
-- 注意：请将 'your_strong_password' 替换为您的强密码
CREATE USER IF NOT EXISTS 'danmu_user'@'localhost' IDENTIFIED BY 'your_strong_password';

-- 授权
GRANT ALL PRIVILEGES ON danmu_db.* TO 'danmu_user'@'localhost';
FLUSH PRIVILEGES;

-- 显示创建结果
SELECT 'Database and user created successfully!' as Status;
SHOW DATABASES LIKE 'danmu_db';
SELECT User, Host FROM mysql.user WHERE User = 'danmu_user';
