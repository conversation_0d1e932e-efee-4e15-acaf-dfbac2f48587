# LoveStory弹幕服务本地部署指南

## 系统要求

- **Python**: 3.8 或更高版本
- **MySQL**: 8.0 或更高版本（或兼容的数据库如 TiDB）
- **操作系统**: Windows 10/11, macOS, Linux
- **内存**: 至少 4GB RAM
- **存储**: 至少 2GB 可用空间

## 快速开始

### 方式一：直接 Python 运行（推荐用于开发）

#### 1. 环境准备

```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd lovestory_danmu_server-main

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

#### 2. 数据库配置

**选项A: 使用本地 MySQL**
```bash
# 安装 MySQL 8.0+
# 创建数据库和用户
mysql -u root -p
```

```sql
CREATE DATABASE danmuapi CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'danmuapi'@'localhost' IDENTIFIED BY 'danmuapi';
GRANT ALL PRIVILEGES ON danmuapi.* TO 'danmuapi'@'localhost';
FLUSH PRIVILEGES;
```

**选项B: 使用现有的 TiDB Cloud（配置文件中已配置）**
- 项目已配置了 TiDB Cloud 连接
- 可以直接使用，无需额外配置

#### 3. 配置文件设置

创建本地配置文件：

```bash
cp config/config.yml config/config.local.yml
```

编辑 `config/config.local.yml`：

```yaml
# 服务器配置
server:
  host: "127.0.0.1"  # 本地访问
  port: 7768

# 数据库配置（如果使用本地MySQL）
database:
  host: "127.0.0.1"
  port: 3306
  user: "danmuapi"
  password: "danmuapi"
  name: "danmuapi"

# JWT 配置
jwt:
  secret_key: "your-secret-key-here"  # 请修改为复杂密钥
  algorithm: "HS256"
  access_token_expire_minutes: 1440
```

#### 4. 启动服务

```bash
# 方式1: 直接运行
python -m src.main

# 方式2: 使用 uvicorn（推荐用于开发）
uvicorn src.main:app --host 127.0.0.1 --port 7768 --reload
```

### 方式二：Docker 运行（推荐用于生产）

#### 1. 安装 Docker 和 Docker Compose

- [Docker Desktop](https://www.docker.com/products/docker-desktop/)

#### 2. 配置环境变量

创建 `.env` 文件：

```bash
# JWT 密钥（请修改为复杂密钥）
JWT_SECRET_KEY=your-very-secret-key-here

# 数据库配置（如果使用本地MySQL）
DANMUAPI_DATABASE__HOST=127.0.0.1
DANMUAPI_DATABASE__PORT=3306
DANMUAPI_DATABASE__USER=danmuapi
DANMUAPI_DATABASE__PASSWORD=danmuapi
DANMUAPI_DATABASE__NAME=danmuapi
```

#### 3. 启动服务

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 访问服务

启动成功后，可以通过以下地址访问：

- **Web 界面**: http://localhost:7768
- **API 文档**: http://localhost:7768/docs
- **API 规范**: http://localhost:7768/redoc

## 初始设置

### 1. 创建管理员账户

首次启动时，系统会自动创建默认管理员账户：
- **用户名**: `admin`
- **密码**: `admin`

**⚠️ 重要**: 首次登录后请立即修改密码！

### 2. 配置弹幕源

1. 登录 Web 界面
2. 进入 "设置" → "搜索源"
3. 启用需要的弹幕源（B站、腾讯视频等）
4. 调整优先级和并发设置

### 3. 测试弹幕搜索

1. 在主页搜索框输入影视作品名称
2. 选择匹配的结果
3. 点击 "导入弹幕" 测试功能

## 性能优化配置

### 1. 数据库优化

在 MySQL 配置文件中添加：

```ini
[mysqld]
innodb_buffer_pool_size = 128M
bulk_insert_buffer_size = 8M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
```

### 2. 应用配置

编辑 `config/performance.yml`：

```yaml
# 根据您的硬件配置调整
task_manager:
  max_concurrent_tasks: 3  # CPU核心数
  task_timeout: 3600       # 1小时

database_pool:
  minsize: 3
  maxsize: 10              # 根据数据库连接限制调整
```

## 常见问题解决

### 1. 端口被占用

```bash
# 查看端口占用
netstat -ano | findstr :7768  # Windows
lsof -i :7768                 # macOS/Linux

# 修改端口
# 编辑 config/config.yml 中的 server.port
```

### 2. 数据库连接失败

```bash
# 检查数据库服务状态
systemctl status mysql        # Linux
brew services list mysql      # macOS

# 测试连接
mysql -h 127.0.0.1 -P 3306 -u danmuapi -p
```

### 3. 依赖安装失败

```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 4. 权限问题（Linux/macOS）

```bash
# 给脚本执行权限
chmod +x run.sh

# 检查文件权限
ls -la config/
```

## 开发环境设置

### 1. IDE 配置

推荐使用 VS Code 并安装以下扩展：
- Python
- Pylance
- Python Docstring Generator

### 2. 代码格式化

```bash
# 安装开发依赖
pip install black isort flake8

# 格式化代码
black src/
isort src/
```

### 3. 调试配置

VS Code `launch.json`：

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python: FastAPI",
            "type": "python",
            "request": "launch",
            "module": "src.main",
            "console": "integratedTerminal",
            "cwd": "${workspaceFolder}"
        }
    ]
}
```

## 日志和监控

### 1. 日志位置

- **应用日志**: `config/logs/app.log`
- **错误日志**: `config/logs/error.log`
- **性能日志**: `config/logs/performance.log`

### 2. 监控端点

- **健康检查**: http://localhost:7768/health
- **性能统计**: http://localhost:7768/api/ui/performance/stats
- **任务状态**: http://localhost:7768/api/ui/tasks

## 备份和恢复

### 1. 数据库备份

```bash
mysqldump -u danmuapi -p danmuapi > backup.sql
```

### 2. 配置备份

```bash
tar -czf config_backup.tar.gz config/
```

## 更新升级

### 1. 代码更新

```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

### 2. 数据库迁移

```bash
# 系统会自动检查并执行数据库迁移
python -m src.main
```

## 技术支持

如果遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 检查 GitHub Issues 中的已知问题
3. 提供详细的错误信息和环境配置

---

**祝您使用愉快！** 🎉
