#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库TLS连接测试脚本
用于验证远程数据库的SSL/TLS连接配置
"""

import sys
import os
import asyncio
import ssl
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

async def test_database_connection():
    """测试数据库连接"""
    try:
        # 导入配置
        from src.config import settings
        import aiomysql
        
        print("=" * 60)
        print("🔍 数据库连接测试")
        print("=" * 60)
        
        # 显示配置信息
        print(f"📍 数据库地址: {settings.database.host}:{settings.database.port}")
        print(f"👤 用户名: {settings.database.user}")
        print(f"🗄️  数据库: {settings.database.name}")
        print(f"🔐 SSL启用: {settings.database.ssl_enabled}")
        
        if settings.database.ssl_enabled:
            print(f"🔒 证书验证: {settings.database.ssl_verify_cert}")
            if settings.database.ssl_ca_path:
                print(f"📜 CA证书: {settings.database.ssl_ca_path}")
            if settings.database.ssl_cert_path:
                print(f"📄 客户端证书: {settings.database.ssl_cert_path}")
        
        print("-" * 60)
        
        # 准备连接参数
        connect_params = {
            'host': settings.database.host,
            'port': settings.database.port,
            'user': settings.database.user,
            'password': settings.database.password,
            'db': settings.database.name,
            'charset': 'utf8mb4',
            'connect_timeout': 10
        }
        
        # 配置SSL/TLS
        if settings.database.ssl_enabled:
            print("🔧 配置SSL连接...")
            ssl_context = ssl.create_default_context()
            
            # 设置证书验证模式
            if settings.database.ssl_verify_cert:
                ssl_context.check_hostname = True
                ssl_context.verify_mode = ssl.CERT_REQUIRED
                print("✅ 启用证书验证")
            else:
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                print("⚠️  禁用证书验证（不推荐用于生产环境）")
            
            # 加载CA证书
            if settings.database.ssl_ca_path:
                ca_path = Path(settings.database.ssl_ca_path)
                if ca_path.exists():
                    ssl_context.load_verify_locations(cafile=str(ca_path))
                    print(f"✅ 已加载CA证书: {ca_path}")
                else:
                    print(f"❌ CA证书文件不存在: {ca_path}")
                    return False
            
            # 加载客户端证书
            if settings.database.ssl_cert_path and settings.database.ssl_key_path:
                cert_path = Path(settings.database.ssl_cert_path)
                key_path = Path(settings.database.ssl_key_path)
                
                if cert_path.exists() and key_path.exists():
                    ssl_context.load_cert_chain(
                        certfile=str(cert_path),
                        keyfile=str(key_path)
                    )
                    print(f"✅ 已加载客户端证书: {cert_path}")
                else:
                    print(f"❌ 客户端证书或私钥文件不存在")
                    print(f"   证书: {cert_path} ({'存在' if cert_path.exists() else '不存在'})")
                    print(f"   私钥: {key_path} ({'存在' if key_path.exists() else '不存在'})")
                    return False
            
            # 设置最低TLS版本
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_2
            connect_params['ssl'] = ssl_context
        
        # 尝试连接
        print("🔌 正在连接数据库...")
        conn = await aiomysql.connect(**connect_params)
        
        print("✅ 数据库连接成功！")
        
        # 测试基本查询
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT VERSION() as version, @@ssl_cipher as ssl_cipher")
            result = await cursor.fetchone()
            
            if result:
                version, ssl_cipher = result
                print(f"📊 数据库版本: {version}")
                if ssl_cipher:
                    print(f"🔐 SSL加密套件: {ssl_cipher}")
                    print("✅ TLS连接已建立并正常工作")
                else:
                    print("⚠️  连接未使用SSL加密")
            
            # 测试数据库权限
            await cursor.execute("SHOW TABLES")
            tables = await cursor.fetchall()
            print(f"📋 数据库中的表数量: {len(tables)}")
        
        conn.close()
        print("✅ 连接测试完成")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保已安装所有依赖: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        print("\n🔧 故障排除建议:")
        print("1. 检查数据库地址和端口是否正确")
        print("2. 验证用户名和密码")
        print("3. 确认数据库服务器允许远程连接")
        print("4. 检查防火墙设置")
        print("5. 验证SSL证书文件路径和权限")
        print("6. 确认数据库服务器启用了SSL/TLS")
        return False

def main():
    """主函数"""
    print("🚀 LoveStory弹幕服务 - 数据库连接测试工具")
    
    # 检查配置文件
    config_file = Path("config/config.yml")
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        print("💡 请确保config/config.yml文件存在并配置正确")
        sys.exit(1)
    
    # 运行测试
    success = asyncio.run(test_database_connection())
    
    if success:
        print("\n🎉 数据库连接测试通过！")
        print("💡 您现在可以启动应用程序了")
        sys.exit(0)
    else:
        print("\n❌ 数据库连接测试失败")
        print("💡 请根据上述建议检查配置后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()
