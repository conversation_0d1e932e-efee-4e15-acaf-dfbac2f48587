#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL证书配置向导
帮助用户配置远程数据库的TLS连接
"""

import os
import sys
import shutil
from pathlib import Path

def print_header():
    """打印标题"""
    print("=" * 60)
    print("🔐 LoveStory弹幕服务 - SSL证书配置向导")
    print("=" * 60)

def create_ssl_directory():
    """创建SSL证书目录"""
    ssl_dir = Path("config/ssl")
    ssl_dir.mkdir(parents=True, exist_ok=True)
    print(f"✅ SSL证书目录已创建: {ssl_dir}")
    return ssl_dir

def get_cloud_provider():
    """获取云服务商选择"""
    providers = {
        "1": ("阿里云RDS", "rds-ca-2019.pem", "https://help.aliyun.com/document_detail/96120.html"),
        "2": ("腾讯云CDB", "tencentdb-ca.pem", "https://cloud.tencent.com/document/product/236/33363"),
        "3": ("AWS RDS", "rds-ca-2019-root.pem", "https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html"),
        "4": ("Google Cloud SQL", "server-ca.pem", "https://cloud.google.com/sql/docs/mysql/configure-ssl-instance"),
        "5": ("Azure Database", "BaltimoreCyberTrustRoot.crt.pem", "https://docs.microsoft.com/en-us/azure/mysql/howto-configure-ssl"),
        "6": ("其他/自定义", "ca-cert.pem", "请参考您的数据库服务商文档")
    }
    
    print("\n📋 请选择您的云数据库服务商:")
    for key, (name, _, _) in providers.items():
        print(f"  {key}. {name}")
    
    while True:
        choice = input("\n请输入选项 (1-6): ").strip()
        if choice in providers:
            return providers[choice]
        print("❌ 无效选择，请重新输入")

def setup_certificate(ssl_dir, cert_filename, provider_name, doc_url):
    """设置证书文件"""
    print(f"\n🔧 配置 {provider_name} SSL证书")
    print("-" * 40)
    
    target_path = ssl_dir / "ca-cert.pem"
    
    print(f"📥 请按照以下步骤获取SSL证书:")
    print(f"1. 访问文档: {doc_url}")
    print(f"2. 下载CA证书文件 (通常名为: {cert_filename})")
    print(f"3. 将证书文件重命名为: ca-cert.pem")
    print(f"4. 将文件放置到: {ssl_dir.absolute()}")
    
    input("\n按回车键继续...")
    
    # 检查证书文件是否存在
    if target_path.exists():
        print(f"✅ 发现证书文件: {target_path}")
        
        # 验证证书文件
        try:
            with open(target_path, 'r') as f:
                content = f.read()
                if "BEGIN CERTIFICATE" in content and "END CERTIFICATE" in content:
                    print("✅ 证书文件格式验证通过")
                    return True
                else:
                    print("❌ 证书文件格式不正确")
                    return False
        except Exception as e:
            print(f"❌ 读取证书文件失败: {e}")
            return False
    else:
        print(f"❌ 未找到证书文件: {target_path}")
        print("💡 请确保已将证书文件放置到正确位置")
        return False

def update_config_file():
    """更新配置文件"""
    config_file = Path("config/config.yml")
    
    if not config_file.exists():
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    print(f"\n📝 请手动编辑配置文件: {config_file}")
    print("确保以下配置正确:")
    print("""
database:
  host: "your-remote-db-host.com"
  port: 3306
  user: "your_username"
  password: "your_password"
  name: "your_database"
  ssl_enabled: true
  ssl_verify_cert: true
  ssl_ca_path: "config/ssl/ca-cert.pem"
  ssl_cert_path: null
  ssl_key_path: null
""")
    
    return True

def set_file_permissions(ssl_dir):
    """设置文件权限"""
    if os.name == 'posix':  # Unix/Linux系统
        try:
            # 设置SSL目录权限
            os.chmod(ssl_dir, 0o755)
            
            # 设置证书文件权限
            for cert_file in ssl_dir.glob("*.pem"):
                os.chmod(cert_file, 0o600)
                print(f"✅ 已设置文件权限: {cert_file}")
            
            return True
        except Exception as e:
            print(f"⚠️  设置文件权限失败: {e}")
            print("💡 请手动执行: chmod 600 config/ssl/*.pem")
            return False
    else:
        print("💡 Windows系统无需设置特殊权限")
        return True

def test_connection():
    """测试数据库连接"""
    print("\n🔍 测试数据库连接...")
    
    test_script = Path("test_db_connection.py")
    if test_script.exists():
        print("💡 运行以下命令测试连接:")
        print(f"   python {test_script}")
        
        choice = input("\n是否现在运行测试? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            try:
                import subprocess
                result = subprocess.run([sys.executable, str(test_script)], 
                                      capture_output=True, text=True)
                print(result.stdout)
                if result.stderr:
                    print("错误输出:", result.stderr)
                return result.returncode == 0
            except Exception as e:
                print(f"❌ 运行测试失败: {e}")
                return False
    else:
        print(f"❌ 测试脚本不存在: {test_script}")
    
    return True

def main():
    """主函数"""
    print_header()
    
    try:
        # 1. 创建SSL目录
        ssl_dir = create_ssl_directory()
        
        # 2. 选择云服务商
        provider_name, cert_filename, doc_url = get_cloud_provider()
        
        # 3. 设置证书
        if not setup_certificate(ssl_dir, cert_filename, provider_name, doc_url):
            print("\n❌ 证书配置失败")
            sys.exit(1)
        
        # 4. 设置文件权限
        set_file_permissions(ssl_dir)
        
        # 5. 更新配置文件
        update_config_file()
        
        # 6. 测试连接
        test_connection()
        
        print("\n" + "=" * 60)
        print("🎉 SSL证书配置完成！")
        print("=" * 60)
        print("📝 下一步操作:")
        print("1. 编辑 config/config.yml 文件，填入数据库连接信息")
        print("2. 运行 python test_db_connection.py 测试连接")
        print("3. 启动应用程序")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n\n❌ 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 配置过程中发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
