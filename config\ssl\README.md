# SSL/TLS 证书配置说明

## 📁 证书文件目录

将您的SSL证书文件放置在此目录下，用于与远程数据库建立安全连接。

### 证书文件类型

1. **CA证书** (`ca-cert.pem`)
   - 证书颁发机构的根证书
   - 用于验证数据库服务器证书的有效性
   - 通常由数据库服务提供商提供

2. **客户端证书** (`client-cert.pem`)
   - 客户端身份验证证书
   - 用于双向TLS认证（如果数据库要求）
   - 可选，取决于数据库配置

3. **客户端私钥** (`client-key.pem`)
   - 客户端证书对应的私钥文件
   - 与客户端证书配对使用
   - 可选，取决于数据库配置

## 🔧 配置方式

### 方式一：仅使用CA证书（推荐）
```yaml
database:
  ssl_enabled: true
  ssl_verify_cert: true
  ssl_ca_path: "config/ssl/ca-cert.pem"
  ssl_cert_path: null
  ssl_key_path: null
```

### 方式二：使用完整的双向认证
```yaml
database:
  ssl_enabled: true
  ssl_verify_cert: true
  ssl_ca_path: "config/ssl/ca-cert.pem"
  ssl_cert_path: "config/ssl/client-cert.pem"
  ssl_key_path: "config/ssl/client-key.pem"
```

### 方式三：简单TLS连接（不验证证书）
```yaml
database:
  ssl_enabled: true
  ssl_verify_cert: false
  ssl_ca_path: null
  ssl_cert_path: null
  ssl_key_path: null
```

## 🛡️ 安全建议

1. **文件权限**: 确保证书文件权限设置正确
   ```bash
   chmod 600 config/ssl/*.pem
   ```

2. **证书验证**: 生产环境建议开启 `ssl_verify_cert: true`

3. **证书更新**: 定期检查证书有效期并及时更新

4. **备份**: 妥善保管证书文件的备份

## 📋 常见数据库服务商证书获取

### 阿里云RDS
- 下载地址：RDS控制台 → 数据安全性 → SSL
- 文件名：通常为 `rds-ca-2019.pem`

### 腾讯云CDB
- 下载地址：CDB控制台 → 数据安全 → SSL加密
- 文件名：通常为 `tencentdb-ca.pem`

### AWS RDS
- 下载地址：AWS文档提供的证书包
- 文件名：通常为 `rds-ca-2019-root.pem`

### Google Cloud SQL
- 下载地址：Cloud SQL控制台 → 连接 → 安全
- 文件名：通常为 `server-ca.pem`

### Azure Database
- 下载地址：Azure门户 → 连接安全性
- 文件名：通常为 `BaltimoreCyberTrustRoot.crt.pem`

## 🔍 故障排除

### 连接失败
1. 检查证书文件路径是否正确
2. 验证证书文件权限
3. 确认数据库服务器支持TLS
4. 检查防火墙设置

### 证书验证失败
1. 确认CA证书是否正确
2. 检查证书是否过期
3. 验证主机名是否匹配

### 示例错误和解决方案
```
SSL connection error: certificate verify failed
→ 检查ssl_ca_path路径和证书有效性

SSL connection error: wrong version number
→ 确认数据库端口和SSL配置正确

SSL connection error: connection refused
→ 检查数据库服务器SSL是否启用
```

---

**注意**: 请根据您的数据库服务商提供的具体证书文件进行配置。
