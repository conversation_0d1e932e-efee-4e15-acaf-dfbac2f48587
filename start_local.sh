#!/bin/bash

# LoveStory弹幕服务本地启动脚本 (Linux/macOS)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo ""
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                LoveStory弹幕服务 - 本地启动器                  ║"
    echo "║                LoveStory Danmu Server - Local Launcher       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
}

# 检查命令是否存在
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# 检查Python版本
check_python() {
    echo -e "${BLUE}🔍 检查Python版本...${NC}"
    
    if ! command_exists python3; then
        echo -e "${RED}❌ 未找到Python3，请先安装Python 3.8或更高版本${NC}"
        exit 1
    fi
    
    python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    echo -e "${GREEN}✅ Python版本: $python_version${NC}"
    
    # 检查版本是否满足要求
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 8) else 1)"; then
        echo -e "${RED}❌ Python版本过低，需要3.8或更高版本${NC}"
        exit 1
    fi
}

# 检查项目目录
check_project_dir() {
    if [ ! -f "src/main.py" ]; then
        echo -e "${RED}❌ 请在项目根目录中运行此脚本${NC}"
        exit 1
    fi
}

# 设置虚拟环境
setup_venv() {
    echo -e "${BLUE}📦 准备Python环境...${NC}"
    
    if [ ! -d "venv" ]; then
        echo -e "${YELLOW}🔧 创建虚拟环境...${NC}"
        python3 -m venv venv
    fi
    
    echo -e "${BLUE}🔄 激活虚拟环境...${NC}"
    source venv/bin/activate
    
    echo -e "${BLUE}📥 安装/更新依赖...${NC}"
    pip install -r requirements.txt
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境准备完成${NC}"
}

# 设置配置文件
setup_config() {
    echo -e "${BLUE}⚙️  检查配置文件...${NC}"
    
    if [ ! -f "config/config.local.yml" ]; then
        echo -e "${YELLOW}📝 创建本地配置文件...${NC}"
        
        cat > config/config.local.yml << 'EOF'
# 本地开发配置
# 服务器配置
server:
  host: "127.0.0.1"
  port: 7768

# 数据库配置 - 请根据实际情况修改
database:
  host: "127.0.0.1"
  port: 3306
  user: "danmuapi"
  password: "danmuapi"
  name: "danmuapi"

# JWT 配置
jwt:
  secret_key: "local-dev-secret-key-please-change-in-production"
  algorithm: "HS256"
  access_token_expire_minutes: 1440
EOF
        
        echo -e "${GREEN}✅ 已创建 config/config.local.yml${NC}"
        echo -e "${YELLOW}⚠️  请根据需要修改数据库配置${NC}"
    fi
}

# 创建必要目录
create_directories() {
    echo -e "${BLUE}📁 创建必要目录...${NC}"
    
    mkdir -p config/logs
    mkdir -p data
    mkdir -p temp
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 启动服务
start_server() {
    echo ""
    echo -e "${GREEN}🚀 启动LoveStory弹幕服务...${NC}"
    echo "============================================================"
    echo "访问地址: http://127.0.0.1:7768"
    echo "API文档: http://127.0.0.1:7768/docs"
    echo "按 Ctrl+C 停止服务"
    echo "============================================================"
    echo ""
    
    # 设置环境变量
    export CONFIG_FILE="config/config.local.yml"
    
    # 启动服务
    python3 start_local.py
}

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}👋 服务已停止${NC}"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 主函数
main() {
    print_banner
    check_python
    check_project_dir
    setup_venv
    setup_config
    create_directories
    start_server
}

# 运行主函数
main "$@"
