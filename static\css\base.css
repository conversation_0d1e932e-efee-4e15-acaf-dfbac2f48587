:root {
    --primary-color: #4a90e2;
    --secondary-color: #f5f7fa;
    --text-color: #333;
    --border-color: #dcdfe6;
    --error-color: #f56c6c;
    --success-color: #67c23a;
    --bg-color: #fff;
    --card-bg: #fff;
    --shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: var(--secondary-color);
    color: var(--text-color);
    line-height: 1.6;
}

#app-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: var(--bg-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
}

h1, h2 {
    color: var(--text-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
}

h1 {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.hidden {
    display: none !important;
}

/* Layout */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.header-title-group {
    display: flex;
    align-items: center;
    gap: 15px;
}

#header-logo {
    width: 50px;
    height: 50px;
    border-radius: 8px;
}

.header-user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.main-layout {
    display: flex;
    height: calc(100vh - 120px); 
}

#sidebar {
    width: 200px;
    background-color: var(--secondary-color);
    padding: 20px 0;
    border-right: 1px solid var(--border-color);
    flex-shrink: 0;
}

#sidebar ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

#sidebar .nav-link {
    display: block;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    border-left: 3px solid transparent;
    transition: all 0.2s ease-in-out;
}

#sidebar .nav-link:hover {
    background-color: #e9ecef;
}

#sidebar .nav-link.active {
    color: var(--primary-color);
    background-color: #e9ecef;
    border-left-color: var(--primary-color);
}

#main-content {
    flex-grow: 1;
    padding: 20px;
    overflow-y: auto;
}
